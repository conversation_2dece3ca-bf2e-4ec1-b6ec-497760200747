{"prefix": "reproduce", "dataset": "cars", "memory_size": 0, "shuffle": true, "init_cls": 28, "increment": 28, "model_name": "adam_adapter", "backbone_type": "pretrained_vit_b16_224_adapter", "device": ["0"], "shot": 5, "resume": false, "seed": [1993], "tuned_epoch": 40, "init_lr": 0.01, "batch_size": 48, "weight_decay": 0.0005, "min_lr": 0, "ffn_num": 64, "optimizer": "sgd", "calibration": true, "fecam": true, "knn_k": 5, "knn_distance_metric": "cosine", "knn_weight_decay": 0.1, "knn_adaptive_k": true, "knn_temperature": 16.0, "k_min": 3, "k_max": 21, "dynamic_k_method": "cosine_similarity", "cosine_temperature": 16.0, "use_wasserstein": true, "wasserstein_alpha": 0.5, "wasserstein_max_iter": 20, "wasserstein_tol": 1e-05, "wasserstein_reg_lambda": 1e-06, "wasserstein_verbose": false}