2025-07-14 14:21:25,657 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 14:21:25,657 [trainer.py] => prefix: reproduce
2025-07-14 14:21:25,657 [trainer.py] => dataset: cub
2025-07-14 14:21:25,658 [trainer.py] => memory_size: 0
2025-07-14 14:21:25,658 [trainer.py] => shuffle: True
2025-07-14 14:21:25,658 [trainer.py] => init_cls: 100
2025-07-14 14:21:25,658 [trainer.py] => increment: 10
2025-07-14 14:21:25,658 [trainer.py] => model_name: ranpac
2025-07-14 14:21:25,658 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 14:21:25,658 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 14:21:25,658 [trainer.py] => seed: 1993
2025-07-14 14:21:25,658 [trainer.py] => resume: False
2025-07-14 14:21:25,659 [trainer.py] => shot: 5
2025-07-14 14:21:25,659 [trainer.py] => use_simplecil: False
2025-07-14 14:21:25,659 [trainer.py] => tuned_epoch: 1
2025-07-14 14:21:25,659 [trainer.py] => init_lr: 0.01
2025-07-14 14:21:25,659 [trainer.py] => batch_size: 48
2025-07-14 14:21:25,659 [trainer.py] => weight_decay: 0.0005
2025-07-14 14:21:25,659 [trainer.py] => min_lr: 0
2025-07-14 14:21:25,659 [trainer.py] => ffn_num: 64
2025-07-14 14:21:25,659 [trainer.py] => optimizer: sgd
2025-07-14 14:21:25,660 [trainer.py] => use_RP: True
2025-07-14 14:21:25,660 [trainer.py] => M: 10000
2025-07-14 14:21:25,660 [trainer.py] => fecam: False
2025-07-14 14:21:25,660 [trainer.py] => calibration: True
2025-07-14 14:21:25,882 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 14:22:03,914 [trainer.py] => All params: 86988288
2025-07-14 14:22:03,915 [trainer.py] => Trainable params: 1189632
2025-07-14 14:22:13,464 [ranpac.py] => Learning on 0-100
2025-07-14 14:23:25,877 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 14:23:58,960 [trainer.py] => No NME accuracy.
2025-07-14 14:23:58,960 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 14:23:58,960 [trainer.py] => CNN HM: [0.0]
2025-07-14 14:23:58,960 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 14:23:58,960 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 14:23:58,962 [trainer.py] => All params: 87988289
2025-07-14 14:23:58,964 [trainer.py] => Trainable params: 1189633
2025-07-14 14:23:59,157 [ranpac.py] => Learning on 100-110
2025-07-14 14:24:19,718 [trainer.py] => No NME accuracy.
2025-07-14 14:24:19,719 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 14:24:19,719 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 14:24:19,719 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 14:24:19,719 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 14:24:19,721 [trainer.py] => All params: 88088289
2025-07-14 14:24:19,723 [trainer.py] => Trainable params: 2289633
2025-07-14 14:24:19,738 [ranpac.py] => Learning on 110-120
2025-07-14 14:24:39,253 [trainer.py] => No NME accuracy.
2025-07-14 14:24:39,254 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 14:24:39,254 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 14:24:39,254 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 14:24:39,254 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 14:24:39,256 [trainer.py] => All params: 88188289
2025-07-14 14:24:39,257 [trainer.py] => Trainable params: 2389633
2025-07-14 14:24:39,273 [ranpac.py] => Learning on 120-130
2025-07-14 14:25:00,477 [trainer.py] => No NME accuracy.
2025-07-14 14:25:00,478 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 14:25:00,478 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 14:25:00,478 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 14:25:00,478 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 14:25:00,480 [trainer.py] => All params: 88288289
2025-07-14 14:25:00,481 [trainer.py] => Trainable params: 2489633
2025-07-14 14:25:00,505 [ranpac.py] => Learning on 130-140
2025-07-14 14:25:23,136 [trainer.py] => No NME accuracy.
2025-07-14 14:25:23,136 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 14:25:23,136 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 14:25:23,137 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 14:25:23,137 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 14:25:23,138 [trainer.py] => All params: 88388289
2025-07-14 14:25:23,140 [trainer.py] => Trainable params: 2589633
2025-07-14 14:25:23,157 [ranpac.py] => Learning on 140-150
2025-07-14 14:25:45,127 [trainer.py] => No NME accuracy.
2025-07-14 14:25:45,127 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 14:25:45,128 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 14:25:45,128 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 14:25:45,128 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 14:25:45,129 [trainer.py] => All params: 88488289
2025-07-14 14:25:45,130 [trainer.py] => Trainable params: 2689633
2025-07-14 14:25:45,150 [ranpac.py] => Learning on 150-160
2025-07-14 14:26:08,105 [trainer.py] => No NME accuracy.
2025-07-14 14:26:08,105 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 14:26:08,105 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 14:26:08,105 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 14:26:08,105 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 14:26:08,106 [trainer.py] => All params: 88588289
2025-07-14 14:26:08,107 [trainer.py] => Trainable params: 2789633
2025-07-14 14:26:08,129 [ranpac.py] => Learning on 160-170
2025-07-14 14:26:33,416 [trainer.py] => No NME accuracy.
2025-07-14 14:26:33,417 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 14:26:33,418 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 14:26:33,418 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 14:26:33,419 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 14:26:33,421 [trainer.py] => All params: 88688289
2025-07-14 14:26:33,422 [trainer.py] => Trainable params: 2889633
2025-07-14 14:26:33,449 [ranpac.py] => Learning on 170-180
2025-07-14 14:27:01,175 [trainer.py] => No NME accuracy.
2025-07-14 14:27:01,176 [trainer.py] => CNN: {'total': 81.88, '00-99': 86.55, '100-109': 88.51, '110-119': 71.48, '120-129': 67.24, '130-139': 81.03, '140-149': 64.03, '150-159': 79.45, '160-169': 77.18, '170-179': 78.86, 'old': 82.07, 'new': 78.86}
2025-07-14 14:27:01,179 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433]
2025-07-14 14:27:01,179 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88]
2025-07-14 14:27:01,179 [trainer.py] => Average Accuracy (CNN): 85.61555555555556 

2025-07-14 14:27:01,180 [trainer.py] => All params: 88788289
2025-07-14 14:27:01,181 [trainer.py] => Trainable params: 2989633
2025-07-14 14:27:01,204 [ranpac.py] => Learning on 180-190
2025-07-14 14:27:26,426 [trainer.py] => No NME accuracy.
2025-07-14 14:27:26,427 [trainer.py] => CNN: {'total': 81.03, '00-99': 85.79, '100-109': 88.85, '110-119': 72.18, '120-129': 67.93, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.29, 'new': 76.31}
2025-07-14 14:27:26,427 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721]
2025-07-14 14:27:26,427 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03]
2025-07-14 14:27:26,427 [trainer.py] => Average Accuracy (CNN): 85.15700000000001 

2025-07-14 14:27:26,428 [trainer.py] => All params: 88888289
2025-07-14 14:27:26,429 [trainer.py] => Trainable params: 3089633
2025-07-14 14:27:26,456 [ranpac.py] => Learning on 190-200
2025-07-14 14:27:52,765 [trainer.py] => No NME accuracy.
2025-07-14 14:27:52,766 [trainer.py] => CNN: {'total': 80.17, '00-99': 84.92, '100-109': 88.51, '110-119': 73.59, '120-129': 68.28, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.52, '180-189': 74.91, '190-199': 72.97, 'old': 80.56, 'new': 72.97}
2025-07-14 14:27:52,766 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721, 76.577]
2025-07-14 14:27:52,766 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03, 80.17]
2025-07-14 14:27:52,766 [trainer.py] => Average Accuracy (CNN): 84.70363636363636 

2025-07-14 14:27:52,767 [trainer.py] => Forgetting (CNN): 2.0150000000000006
2025-07-14 15:15:40,831 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 15:15:40,832 [trainer.py] => prefix: reproduce
2025-07-14 15:15:40,832 [trainer.py] => dataset: cub
2025-07-14 15:15:40,833 [trainer.py] => memory_size: 0
2025-07-14 15:15:40,833 [trainer.py] => shuffle: True
2025-07-14 15:15:40,834 [trainer.py] => init_cls: 100
2025-07-14 15:15:40,834 [trainer.py] => increment: 10
2025-07-14 15:15:40,834 [trainer.py] => model_name: ranpac
2025-07-14 15:15:40,835 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 15:15:40,835 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 15:15:40,835 [trainer.py] => seed: 1993
2025-07-14 15:15:40,836 [trainer.py] => resume: False
2025-07-14 15:15:40,836 [trainer.py] => shot: 5
2025-07-14 15:15:40,836 [trainer.py] => use_simplecil: False
2025-07-14 15:15:40,837 [trainer.py] => tuned_epoch: 1
2025-07-14 15:15:40,837 [trainer.py] => init_lr: 0.01
2025-07-14 15:15:40,837 [trainer.py] => batch_size: 48
2025-07-14 15:15:40,838 [trainer.py] => weight_decay: 0.0005
2025-07-14 15:15:40,838 [trainer.py] => min_lr: 0
2025-07-14 15:15:40,838 [trainer.py] => ffn_num: 64
2025-07-14 15:15:40,839 [trainer.py] => optimizer: sgd
2025-07-14 15:15:40,839 [trainer.py] => use_RP: True
2025-07-14 15:15:40,839 [trainer.py] => M: 10000
2025-07-14 15:15:40,840 [trainer.py] => fecam: False
2025-07-14 15:15:40,840 [trainer.py] => calibration: True
2025-07-14 15:15:40,840 [trainer.py] => knn_k: 5
2025-07-14 15:15:41,045 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 15:16:13,718 [trainer.py] => All params: 86988288
2025-07-14 15:16:13,719 [trainer.py] => Trainable params: 1189632
2025-07-14 15:16:24,922 [ranpac.py] => Learning on 0-100
2025-07-14 15:17:28,834 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 15:18:17,681 [trainer.py] => No NME accuracy.
2025-07-14 15:18:17,682 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 15:18:17,682 [trainer.py] => CNN HM: [0.0]
2025-07-14 15:18:17,682 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 15:18:17,682 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 15:18:17,684 [trainer.py] => All params: 87988289
2025-07-14 15:18:17,686 [trainer.py] => Trainable params: 1189633
2025-07-14 15:18:17,913 [ranpac.py] => Learning on 100-110
2025-07-14 15:18:49,026 [trainer.py] => No NME accuracy.
2025-07-14 15:18:49,027 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 15:18:49,027 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 15:18:49,027 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 15:18:49,027 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 15:18:49,029 [trainer.py] => All params: 88088289
2025-07-14 15:18:49,030 [trainer.py] => Trainable params: 2289633
2025-07-14 15:18:49,045 [ranpac.py] => Learning on 110-120
2025-07-14 15:19:13,496 [trainer.py] => No NME accuracy.
2025-07-14 15:19:13,496 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 15:19:13,496 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 15:19:13,496 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 15:19:13,497 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 15:19:13,499 [trainer.py] => All params: 88188289
2025-07-14 15:19:13,500 [trainer.py] => Trainable params: 2389633
2025-07-14 15:19:13,515 [ranpac.py] => Learning on 120-130
2025-07-14 15:19:42,975 [trainer.py] => No NME accuracy.
2025-07-14 15:19:42,976 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 15:19:42,976 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 15:19:42,976 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 15:19:42,976 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 15:19:42,978 [trainer.py] => All params: 88288289
2025-07-14 15:19:42,980 [trainer.py] => Trainable params: 2489633
2025-07-14 15:19:42,998 [ranpac.py] => Learning on 130-140
2025-07-14 15:20:09,722 [trainer.py] => No NME accuracy.
2025-07-14 15:20:09,722 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 15:20:09,722 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 15:20:09,722 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 15:20:09,723 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 15:20:09,724 [trainer.py] => All params: 88388289
2025-07-14 15:20:09,726 [trainer.py] => Trainable params: 2589633
2025-07-14 15:20:09,744 [ranpac.py] => Learning on 140-150
2025-07-14 15:20:41,398 [trainer.py] => No NME accuracy.
2025-07-14 15:20:41,398 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 15:20:41,398 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 15:20:41,399 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 15:20:41,399 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 15:20:41,400 [trainer.py] => All params: 88488289
2025-07-14 15:20:41,402 [trainer.py] => Trainable params: 2689633
2025-07-14 15:20:41,422 [ranpac.py] => Learning on 150-160
2025-07-14 15:21:20,010 [trainer.py] => No NME accuracy.
2025-07-14 15:21:20,010 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 15:21:20,011 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 15:21:20,011 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 15:21:20,011 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 15:21:20,012 [trainer.py] => All params: 88588289
2025-07-14 15:21:20,014 [trainer.py] => Trainable params: 2789633
2025-07-14 15:21:20,034 [ranpac.py] => Learning on 160-170
2025-07-14 15:21:51,007 [trainer.py] => No NME accuracy.
2025-07-14 15:21:51,008 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 15:21:51,008 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 15:21:51,008 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 15:21:51,008 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 15:21:51,009 [trainer.py] => All params: 88688289
2025-07-14 15:21:51,010 [trainer.py] => Trainable params: 2889633
2025-07-14 15:21:51,030 [ranpac.py] => Learning on 170-180
2025-07-14 15:22:25,531 [trainer.py] => No NME accuracy.
2025-07-14 15:22:25,532 [trainer.py] => CNN: {'total': 81.88, '00-99': 86.55, '100-109': 88.51, '110-119': 71.48, '120-129': 67.24, '130-139': 81.03, '140-149': 64.03, '150-159': 79.45, '160-169': 77.18, '170-179': 78.86, 'old': 82.07, 'new': 78.86}
2025-07-14 15:22:25,532 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433]
2025-07-14 15:22:25,532 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88]
2025-07-14 15:22:25,532 [trainer.py] => Average Accuracy (CNN): 85.61555555555556 

2025-07-14 15:22:25,533 [trainer.py] => All params: 88788289
2025-07-14 15:22:25,534 [trainer.py] => Trainable params: 2989633
2025-07-14 15:22:25,553 [ranpac.py] => Learning on 180-190
2025-07-14 15:22:58,794 [trainer.py] => No NME accuracy.
2025-07-14 15:22:58,795 [trainer.py] => CNN: {'total': 81.03, '00-99': 85.79, '100-109': 88.85, '110-119': 72.18, '120-129': 67.93, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.29, 'new': 76.31}
2025-07-14 15:22:58,795 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721]
2025-07-14 15:22:58,795 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03]
2025-07-14 15:22:58,795 [trainer.py] => Average Accuracy (CNN): 85.15700000000001 

2025-07-14 15:22:58,796 [trainer.py] => All params: 88888289
2025-07-14 15:22:58,797 [trainer.py] => Trainable params: 3089633
2025-07-14 15:22:58,817 [ranpac.py] => Learning on 190-200
2025-07-14 15:23:37,332 [trainer.py] => No NME accuracy.
2025-07-14 15:23:37,332 [trainer.py] => CNN: {'total': 80.17, '00-99': 84.92, '100-109': 88.51, '110-119': 73.59, '120-129': 68.28, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.52, '180-189': 74.91, '190-199': 72.97, 'old': 80.56, 'new': 72.97}
2025-07-14 15:23:37,332 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721, 76.577]
2025-07-14 15:23:37,332 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03, 80.17]
2025-07-14 15:23:37,332 [trainer.py] => Average Accuracy (CNN): 84.70363636363636 

2025-07-14 15:23:37,334 [trainer.py] => Forgetting (CNN): 2.0150000000000006
2025-07-14 15:26:19,081 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 15:26:19,082 [trainer.py] => prefix: reproduce
2025-07-14 15:26:19,082 [trainer.py] => dataset: cub
2025-07-14 15:26:19,082 [trainer.py] => memory_size: 0
2025-07-14 15:26:19,082 [trainer.py] => shuffle: True
2025-07-14 15:26:19,082 [trainer.py] => init_cls: 100
2025-07-14 15:26:19,082 [trainer.py] => increment: 10
2025-07-14 15:26:19,083 [trainer.py] => model_name: ranpac
2025-07-14 15:26:19,083 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 15:26:19,083 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 15:26:19,083 [trainer.py] => seed: 1993
2025-07-14 15:26:19,083 [trainer.py] => resume: False
2025-07-14 15:26:19,083 [trainer.py] => shot: 5
2025-07-14 15:26:19,083 [trainer.py] => use_simplecil: False
2025-07-14 15:26:19,083 [trainer.py] => tuned_epoch: 1
2025-07-14 15:26:19,084 [trainer.py] => init_lr: 0.01
2025-07-14 15:26:19,084 [trainer.py] => batch_size: 48
2025-07-14 15:26:19,084 [trainer.py] => weight_decay: 0.0005
2025-07-14 15:26:19,084 [trainer.py] => min_lr: 0
2025-07-14 15:26:19,084 [trainer.py] => ffn_num: 64
2025-07-14 15:26:19,084 [trainer.py] => optimizer: sgd
2025-07-14 15:26:19,084 [trainer.py] => use_RP: True
2025-07-14 15:26:19,084 [trainer.py] => M: 10000
2025-07-14 15:26:19,084 [trainer.py] => fecam: False
2025-07-14 15:26:19,085 [trainer.py] => calibration: True
2025-07-14 15:26:19,085 [trainer.py] => knn_k: 5
2025-07-14 15:26:19,451 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 15:26:48,549 [trainer.py] => All params: 86988288
2025-07-14 15:26:48,550 [trainer.py] => Trainable params: 1189632
2025-07-14 15:26:50,250 [ranpac.py] => Learning on 0-100
2025-07-14 15:27:30,372 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 15:28:05,692 [trainer.py] => No NME accuracy.
2025-07-14 15:28:05,692 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 15:28:05,692 [trainer.py] => CNN HM: [0.0]
2025-07-14 15:28:05,692 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 15:28:05,693 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 15:28:05,695 [trainer.py] => All params: 87988289
2025-07-14 15:28:05,696 [trainer.py] => Trainable params: 1189633
2025-07-14 15:28:05,710 [ranpac.py] => Learning on 100-110
2025-07-14 15:28:09,097 [ranpac.py] => [KNN] task 1, K=5, knn_k=5
2025-07-14 15:28:29,480 [trainer.py] => No NME accuracy.
2025-07-14 15:28:29,480 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 15:28:29,480 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 15:28:29,480 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 15:28:29,480 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 15:28:29,482 [trainer.py] => All params: 88088289
2025-07-14 15:28:29,483 [trainer.py] => Trainable params: 2289633
2025-07-14 15:28:29,505 [ranpac.py] => Learning on 110-120
2025-07-14 15:28:31,625 [ranpac.py] => [KNN] task 2, K=5, knn_k=5
2025-07-14 15:28:56,384 [trainer.py] => No NME accuracy.
2025-07-14 15:28:56,385 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 15:28:56,385 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 15:28:56,385 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 15:28:56,385 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 15:28:56,387 [trainer.py] => All params: 88188289
2025-07-14 15:28:56,389 [trainer.py] => Trainable params: 2389633
2025-07-14 15:28:56,404 [ranpac.py] => Learning on 120-130
2025-07-14 15:28:59,532 [ranpac.py] => [KNN] task 3, K=5, knn_k=5
2025-07-14 15:29:24,498 [trainer.py] => No NME accuracy.
2025-07-14 15:29:24,499 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 15:29:24,499 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 15:29:24,499 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 15:29:24,499 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 15:29:24,501 [trainer.py] => All params: 88288289
2025-07-14 15:29:24,503 [trainer.py] => Trainable params: 2489633
2025-07-14 15:29:24,519 [ranpac.py] => Learning on 130-140
2025-07-14 15:29:28,431 [ranpac.py] => [KNN] task 4, K=5, knn_k=5
2025-07-14 15:29:52,786 [trainer.py] => No NME accuracy.
2025-07-14 15:29:52,787 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 15:29:52,787 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 15:29:52,788 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 15:29:52,788 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 15:29:52,789 [trainer.py] => All params: 88388289
2025-07-14 15:29:52,790 [trainer.py] => Trainable params: 2589633
2025-07-14 15:29:52,809 [ranpac.py] => Learning on 140-150
2025-07-14 15:29:56,185 [ranpac.py] => [KNN] task 5, K=5, knn_k=5
2025-07-14 15:30:24,518 [trainer.py] => No NME accuracy.
2025-07-14 15:30:24,519 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 15:30:24,519 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 15:30:24,520 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 15:30:24,520 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 15:30:24,522 [trainer.py] => All params: 88488289
2025-07-14 15:30:24,524 [trainer.py] => Trainable params: 2689633
2025-07-14 15:30:24,543 [ranpac.py] => Learning on 150-160
2025-07-14 15:30:27,396 [ranpac.py] => [KNN] task 6, K=5, knn_k=5
2025-07-14 15:30:55,288 [trainer.py] => No NME accuracy.
2025-07-14 15:30:55,289 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 15:30:55,290 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 15:30:55,290 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 15:30:55,291 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 15:30:55,293 [trainer.py] => All params: 88588289
2025-07-14 15:30:55,295 [trainer.py] => Trainable params: 2789633
2025-07-14 15:30:55,318 [ranpac.py] => Learning on 160-170
2025-07-14 15:30:57,849 [ranpac.py] => [KNN] task 7, K=5, knn_k=5
2025-07-14 15:31:29,512 [trainer.py] => No NME accuracy.
2025-07-14 15:31:29,513 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 15:31:29,514 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 15:31:29,514 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 15:31:29,514 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 15:31:29,516 [trainer.py] => All params: 88688289
2025-07-14 15:31:29,517 [trainer.py] => Trainable params: 2889633
2025-07-14 15:31:29,536 [ranpac.py] => Learning on 170-180
2025-07-14 15:31:32,615 [ranpac.py] => [KNN] task 8, K=5, knn_k=5
2025-07-14 15:32:21,761 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 15:32:21,762 [trainer.py] => prefix: reproduce
2025-07-14 15:32:21,762 [trainer.py] => dataset: cub
2025-07-14 15:32:21,762 [trainer.py] => memory_size: 0
2025-07-14 15:32:21,762 [trainer.py] => shuffle: True
2025-07-14 15:32:21,762 [trainer.py] => init_cls: 100
2025-07-14 15:32:21,763 [trainer.py] => increment: 10
2025-07-14 15:32:21,763 [trainer.py] => model_name: ranpac
2025-07-14 15:32:21,763 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 15:32:21,763 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 15:32:21,763 [trainer.py] => seed: 1993
2025-07-14 15:32:21,763 [trainer.py] => resume: False
2025-07-14 15:32:21,763 [trainer.py] => shot: 5
2025-07-14 15:32:21,763 [trainer.py] => use_simplecil: False
2025-07-14 15:32:21,764 [trainer.py] => tuned_epoch: 1
2025-07-14 15:32:21,764 [trainer.py] => init_lr: 0.01
2025-07-14 15:32:21,764 [trainer.py] => batch_size: 48
2025-07-14 15:32:21,764 [trainer.py] => weight_decay: 0.0005
2025-07-14 15:32:21,764 [trainer.py] => min_lr: 0
2025-07-14 15:32:21,764 [trainer.py] => ffn_num: 64
2025-07-14 15:32:21,764 [trainer.py] => optimizer: sgd
2025-07-14 15:32:21,764 [trainer.py] => use_RP: True
2025-07-14 15:32:21,765 [trainer.py] => M: 10000
2025-07-14 15:32:21,765 [trainer.py] => fecam: False
2025-07-14 15:32:21,765 [trainer.py] => calibration: True
2025-07-14 15:32:21,765 [trainer.py] => knn_k: 1
2025-07-14 15:32:21,898 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 15:32:46,669 [trainer.py] => All params: 86988288
2025-07-14 15:32:46,670 [trainer.py] => Trainable params: 1189632
2025-07-14 15:32:48,329 [ranpac.py] => Learning on 0-100
2025-07-14 15:33:26,594 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 15:34:02,767 [trainer.py] => No NME accuracy.
2025-07-14 15:34:02,768 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 15:34:02,768 [trainer.py] => CNN HM: [0.0]
2025-07-14 15:34:02,768 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 15:34:02,768 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 15:34:02,770 [trainer.py] => All params: 87988289
2025-07-14 15:34:02,772 [trainer.py] => Trainable params: 1189633
2025-07-14 15:34:02,786 [ranpac.py] => Learning on 100-110
2025-07-14 15:34:05,440 [ranpac.py] => [KNN] task 1, K=1, knn_k=1
2025-07-14 15:34:26,416 [trainer.py] => No NME accuracy.
2025-07-14 15:34:26,416 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 15:34:26,416 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 15:34:26,417 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 15:34:26,417 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 15:34:26,418 [trainer.py] => All params: 88088289
2025-07-14 15:34:26,419 [trainer.py] => Trainable params: 2289633
2025-07-14 15:34:26,435 [ranpac.py] => Learning on 110-120
2025-07-14 15:34:29,172 [ranpac.py] => [KNN] task 2, K=1, knn_k=1
2025-07-14 15:34:51,037 [trainer.py] => No NME accuracy.
2025-07-14 15:34:51,038 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 15:34:51,039 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 15:34:51,039 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 15:34:51,039 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 15:34:51,041 [trainer.py] => All params: 88188289
2025-07-14 15:34:51,042 [trainer.py] => Trainable params: 2389633
2025-07-14 15:34:51,060 [ranpac.py] => Learning on 120-130
2025-07-14 15:34:54,244 [ranpac.py] => [KNN] task 3, K=1, knn_k=1
2025-07-14 15:35:17,335 [trainer.py] => No NME accuracy.
2025-07-14 15:35:17,336 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 15:35:17,337 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 15:35:17,337 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 15:35:17,338 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 15:35:17,340 [trainer.py] => All params: 88288289
2025-07-14 15:35:17,342 [trainer.py] => Trainable params: 2489633
2025-07-14 15:35:17,361 [ranpac.py] => Learning on 130-140
2025-07-14 15:35:20,722 [ranpac.py] => [KNN] task 4, K=1, knn_k=1
2025-07-14 15:35:42,728 [trainer.py] => No NME accuracy.
2025-07-14 15:35:42,729 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 15:35:42,730 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 15:35:42,730 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 15:35:42,730 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 15:35:42,732 [trainer.py] => All params: 88388289
2025-07-14 15:35:42,733 [trainer.py] => Trainable params: 2589633
2025-07-14 15:35:42,751 [ranpac.py] => Learning on 140-150
2025-07-14 15:35:46,194 [ranpac.py] => [KNN] task 5, K=1, knn_k=1
2025-07-14 15:36:12,003 [trainer.py] => No NME accuracy.
2025-07-14 15:36:12,004 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 15:36:12,004 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 15:36:12,005 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 15:36:12,005 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 15:36:12,007 [trainer.py] => All params: 88488289
2025-07-14 15:36:12,009 [trainer.py] => Trainable params: 2689633
2025-07-14 15:36:12,027 [ranpac.py] => Learning on 150-160
2025-07-14 15:36:14,584 [ranpac.py] => [KNN] task 6, K=1, knn_k=1
2025-07-14 15:36:38,821 [trainer.py] => No NME accuracy.
2025-07-14 15:36:38,822 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 15:36:38,822 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 15:36:38,823 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 15:36:38,823 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 15:36:38,825 [trainer.py] => All params: 88588289
2025-07-14 15:36:38,827 [trainer.py] => Trainable params: 2789633
2025-07-14 15:36:38,846 [ranpac.py] => Learning on 160-170
2025-07-14 15:36:41,935 [ranpac.py] => [KNN] task 7, K=1, knn_k=1
2025-07-14 15:37:10,582 [trainer.py] => No NME accuracy.
2025-07-14 15:37:10,582 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 15:37:10,583 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 15:37:10,583 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 15:37:10,583 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 15:37:10,585 [trainer.py] => All params: 88688289
2025-07-14 15:37:10,586 [trainer.py] => Trainable params: 2889633
2025-07-14 15:37:10,605 [ranpac.py] => Learning on 170-180
2025-07-14 15:37:14,348 [ranpac.py] => [KNN] task 8, K=1, knn_k=1
2025-07-14 15:37:44,536 [trainer.py] => No NME accuracy.
2025-07-14 15:37:44,537 [trainer.py] => CNN: {'total': 81.88, '00-99': 86.55, '100-109': 88.51, '110-119': 71.48, '120-129': 67.24, '130-139': 81.03, '140-149': 64.03, '150-159': 79.45, '160-169': 77.18, '170-179': 78.86, 'old': 82.07, 'new': 78.86}
2025-07-14 15:37:44,537 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433]
2025-07-14 15:37:44,537 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88]
2025-07-14 15:37:44,537 [trainer.py] => Average Accuracy (CNN): 85.61555555555556 

2025-07-14 15:37:44,539 [trainer.py] => All params: 88788289
2025-07-14 15:37:44,539 [trainer.py] => Trainable params: 2989633
2025-07-14 15:37:44,559 [ranpac.py] => Learning on 180-190
2025-07-14 15:37:48,375 [ranpac.py] => [KNN] task 9, K=1, knn_k=1
2025-07-14 15:38:22,425 [trainer.py] => No NME accuracy.
2025-07-14 15:38:22,426 [trainer.py] => CNN: {'total': 81.03, '00-99': 85.79, '100-109': 88.85, '110-119': 72.18, '120-129': 67.93, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.29, 'new': 76.31}
2025-07-14 15:38:22,426 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721]
2025-07-14 15:38:22,426 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03]
2025-07-14 15:38:22,426 [trainer.py] => Average Accuracy (CNN): 85.15700000000001 

2025-07-14 15:38:22,427 [trainer.py] => All params: 88888289
2025-07-14 15:38:22,428 [trainer.py] => Trainable params: 3089633
2025-07-14 15:38:22,450 [ranpac.py] => Learning on 190-200
2025-07-14 15:38:24,964 [ranpac.py] => [KNN] task 10, K=1, knn_k=1
2025-07-14 15:38:57,981 [trainer.py] => No NME accuracy.
2025-07-14 15:38:57,982 [trainer.py] => CNN: {'total': 80.17, '00-99': 84.92, '100-109': 88.51, '110-119': 73.59, '120-129': 68.28, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.52, '180-189': 74.91, '190-199': 72.97, 'old': 80.56, 'new': 72.97}
2025-07-14 15:38:57,982 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721, 76.577]
2025-07-14 15:38:57,982 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03, 80.17]
2025-07-14 15:38:57,982 [trainer.py] => Average Accuracy (CNN): 84.70363636363636 

2025-07-14 15:38:57,983 [trainer.py] => Forgetting (CNN): 2.0150000000000006
2025-07-14 15:42:49,800 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 15:42:49,800 [trainer.py] => prefix: reproduce
2025-07-14 15:42:49,800 [trainer.py] => dataset: cub
2025-07-14 15:42:49,800 [trainer.py] => memory_size: 0
2025-07-14 15:42:49,800 [trainer.py] => shuffle: True
2025-07-14 15:42:49,800 [trainer.py] => init_cls: 100
2025-07-14 15:42:49,801 [trainer.py] => increment: 10
2025-07-14 15:42:49,801 [trainer.py] => model_name: ranpac
2025-07-14 15:42:49,801 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 15:42:49,801 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 15:42:49,801 [trainer.py] => seed: 1993
2025-07-14 15:42:49,801 [trainer.py] => resume: False
2025-07-14 15:42:49,801 [trainer.py] => shot: 5
2025-07-14 15:42:49,801 [trainer.py] => use_simplecil: False
2025-07-14 15:42:49,801 [trainer.py] => tuned_epoch: 1
2025-07-14 15:42:49,802 [trainer.py] => init_lr: 0.01
2025-07-14 15:42:49,802 [trainer.py] => batch_size: 48
2025-07-14 15:42:49,802 [trainer.py] => weight_decay: 0.0005
2025-07-14 15:42:49,802 [trainer.py] => min_lr: 0
2025-07-14 15:42:49,802 [trainer.py] => ffn_num: 64
2025-07-14 15:42:49,802 [trainer.py] => optimizer: sgd
2025-07-14 15:42:49,802 [trainer.py] => use_RP: True
2025-07-14 15:42:49,802 [trainer.py] => M: 10000
2025-07-14 15:42:49,802 [trainer.py] => fecam: False
2025-07-14 15:42:49,803 [trainer.py] => calibration: True
2025-07-14 15:42:49,803 [trainer.py] => knn_k: 1
2025-07-14 15:42:49,928 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 15:43:16,811 [trainer.py] => All params: 86988288
2025-07-14 15:43:16,812 [trainer.py] => Trainable params: 1189632
2025-07-14 15:43:18,431 [ranpac.py] => Learning on 0-100
2025-07-14 15:44:01,733 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 15:44:41,284 [trainer.py] => No NME accuracy.
2025-07-14 15:44:41,285 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 15:44:41,285 [trainer.py] => CNN HM: [0.0]
2025-07-14 15:44:41,285 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 15:44:41,285 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 15:44:41,287 [trainer.py] => All params: 87988289
2025-07-14 15:44:41,288 [trainer.py] => Trainable params: 1189633
2025-07-14 15:44:41,316 [ranpac.py] => Learning on 100-110
2025-07-14 15:44:44,740 [ranpac.py] => [KNN] task 1, K=1, knn_k=1
2025-07-14 15:45:06,153 [trainer.py] => No NME accuracy.
2025-07-14 15:45:06,153 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 15:45:06,153 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 15:45:06,154 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 15:45:06,154 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 15:45:06,156 [trainer.py] => All params: 88088289
2025-07-14 15:45:06,157 [trainer.py] => Trainable params: 2289633
2025-07-14 15:45:06,170 [ranpac.py] => Learning on 110-120
2025-07-14 15:45:08,246 [ranpac.py] => [KNN] task 2, K=1, knn_k=1
2025-07-14 15:45:30,202 [trainer.py] => No NME accuracy.
2025-07-14 15:45:30,203 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 15:45:30,203 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 15:45:30,203 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 15:45:30,203 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 15:45:30,205 [trainer.py] => All params: 88188289
2025-07-14 15:45:30,207 [trainer.py] => Trainable params: 2389633
2025-07-14 15:45:30,222 [ranpac.py] => Learning on 120-130
2025-07-14 15:45:33,237 [ranpac.py] => [KNN] task 3, K=1, knn_k=1
2025-07-14 15:45:58,024 [trainer.py] => No NME accuracy.
2025-07-14 15:45:58,024 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 15:45:58,025 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 15:45:58,025 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 15:45:58,025 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 15:45:58,026 [trainer.py] => All params: 88288289
2025-07-14 15:45:58,027 [trainer.py] => Trainable params: 2489633
2025-07-14 15:45:58,044 [ranpac.py] => Learning on 130-140
2025-07-14 15:46:00,940 [ranpac.py] => [KNN] task 4, K=1, knn_k=1
2025-07-14 15:46:28,023 [trainer.py] => No NME accuracy.
2025-07-14 15:46:28,023 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 15:46:28,023 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 15:46:28,023 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 15:46:28,024 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 15:46:28,026 [trainer.py] => All params: 88388289
2025-07-14 15:46:28,027 [trainer.py] => Trainable params: 2589633
2025-07-14 15:46:28,043 [ranpac.py] => Learning on 140-150
2025-07-14 15:46:30,495 [ranpac.py] => [KNN] task 5, K=1, knn_k=1
2025-07-14 15:47:00,540 [trainer.py] => No NME accuracy.
2025-07-14 15:47:00,541 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 15:47:00,541 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 15:47:00,541 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 15:47:00,541 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 15:47:00,543 [trainer.py] => All params: 88488289
2025-07-14 15:47:00,544 [trainer.py] => Trainable params: 2689633
2025-07-14 15:47:00,564 [ranpac.py] => Learning on 150-160
2025-07-14 15:47:03,873 [ranpac.py] => [KNN] task 6, K=1, knn_k=1
2025-07-14 15:47:39,000 [trainer.py] => No NME accuracy.
2025-07-14 15:47:39,001 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 15:47:39,001 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 15:47:39,001 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 15:47:39,001 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 15:47:39,003 [trainer.py] => All params: 88588289
2025-07-14 15:47:39,004 [trainer.py] => Trainable params: 2789633
2025-07-14 15:47:39,032 [ranpac.py] => Learning on 160-170
2025-07-14 15:47:43,390 [ranpac.py] => [KNN] task 7, K=1, knn_k=1
2025-07-14 15:48:15,278 [trainer.py] => No NME accuracy.
2025-07-14 15:48:15,278 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 15:48:15,278 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 15:48:15,278 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 15:48:15,278 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 15:48:15,280 [trainer.py] => All params: 88688289
2025-07-14 15:48:15,281 [trainer.py] => Trainable params: 2889633
2025-07-14 15:48:15,301 [ranpac.py] => Learning on 170-180
2025-07-14 15:48:17,302 [ranpac.py] => [KNN] task 8, K=1, knn_k=1
2025-07-14 15:48:49,926 [trainer.py] => No NME accuracy.
2025-07-14 15:48:49,927 [trainer.py] => CNN: {'total': 81.88, '00-99': 86.55, '100-109': 88.51, '110-119': 71.48, '120-129': 67.24, '130-139': 81.03, '140-149': 64.03, '150-159': 79.45, '160-169': 77.18, '170-179': 78.86, 'old': 82.07, 'new': 78.86}
2025-07-14 15:48:49,929 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433]
2025-07-14 15:48:49,929 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88]
2025-07-14 15:48:49,929 [trainer.py] => Average Accuracy (CNN): 85.61555555555556 

2025-07-14 15:48:49,930 [trainer.py] => All params: 88788289
2025-07-14 15:48:49,931 [trainer.py] => Trainable params: 2989633
2025-07-14 15:48:49,952 [ranpac.py] => Learning on 180-190
2025-07-14 15:48:53,896 [ranpac.py] => [KNN] task 9, K=1, knn_k=1
2025-07-14 15:49:30,570 [trainer.py] => No NME accuracy.
2025-07-14 15:49:30,570 [trainer.py] => CNN: {'total': 81.03, '00-99': 85.79, '100-109': 88.85, '110-119': 72.18, '120-129': 67.93, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.29, 'new': 76.31}
2025-07-14 15:49:30,570 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721]
2025-07-14 15:49:30,570 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03]
2025-07-14 15:49:30,571 [trainer.py] => Average Accuracy (CNN): 85.15700000000001 

2025-07-14 15:49:30,572 [trainer.py] => All params: 88888289
2025-07-14 15:49:30,573 [trainer.py] => Trainable params: 3089633
2025-07-14 15:49:30,595 [ranpac.py] => Learning on 190-200
2025-07-14 15:49:32,801 [ranpac.py] => [KNN] task 10, K=1, knn_k=1
2025-07-14 15:50:06,064 [trainer.py] => No NME accuracy.
2025-07-14 15:50:06,064 [trainer.py] => CNN: {'total': 80.17, '00-99': 84.92, '100-109': 88.51, '110-119': 73.59, '120-129': 68.28, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.52, '180-189': 74.91, '190-199': 72.97, 'old': 80.56, 'new': 72.97}
2025-07-14 15:50:06,064 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721, 76.577]
2025-07-14 15:50:06,065 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03, 80.17]
2025-07-14 15:50:06,065 [trainer.py] => Average Accuracy (CNN): 84.70363636363636 

2025-07-14 15:50:06,066 [trainer.py] => Forgetting (CNN): 2.0150000000000006
2025-07-14 17:03:49,837 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 17:03:49,838 [trainer.py] => prefix: reproduce
2025-07-14 17:03:49,838 [trainer.py] => dataset: cub
2025-07-14 17:03:49,838 [trainer.py] => memory_size: 0
2025-07-14 17:03:49,838 [trainer.py] => shuffle: True
2025-07-14 17:03:49,838 [trainer.py] => init_cls: 100
2025-07-14 17:03:49,838 [trainer.py] => increment: 10
2025-07-14 17:03:49,839 [trainer.py] => model_name: ranpac
2025-07-14 17:03:49,839 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 17:03:49,839 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 17:03:49,839 [trainer.py] => seed: 1993
2025-07-14 17:03:49,839 [trainer.py] => resume: False
2025-07-14 17:03:49,839 [trainer.py] => shot: 5
2025-07-14 17:03:49,839 [trainer.py] => use_simplecil: False
2025-07-14 17:03:49,839 [trainer.py] => tuned_epoch: 1
2025-07-14 17:03:49,839 [trainer.py] => init_lr: 0.01
2025-07-14 17:03:49,840 [trainer.py] => batch_size: 48
2025-07-14 17:03:49,840 [trainer.py] => weight_decay: 0.0005
2025-07-14 17:03:49,840 [trainer.py] => min_lr: 0
2025-07-14 17:03:49,840 [trainer.py] => ffn_num: 64
2025-07-14 17:03:49,840 [trainer.py] => optimizer: sgd
2025-07-14 17:03:49,840 [trainer.py] => use_RP: True
2025-07-14 17:03:49,840 [trainer.py] => M: 10000
2025-07-14 17:03:49,840 [trainer.py] => fecam: False
2025-07-14 17:03:49,840 [trainer.py] => calibration: True
2025-07-14 17:03:49,840 [trainer.py] => knn_k: 1
2025-07-14 17:03:50,066 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 17:04:12,016 [trainer.py] => All params: 86988288
2025-07-14 17:04:12,017 [trainer.py] => Trainable params: 1189632
2025-07-14 17:04:15,024 [ranpac.py] => Learning on 0-100
2025-07-14 17:04:56,395 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 17:05:39,372 [trainer.py] => No NME accuracy.
2025-07-14 17:05:39,372 [trainer.py] => CNN: {'total': 89.81, '00-99': 89.81, 'old': 0, 'new': 89.81}
2025-07-14 17:05:39,372 [trainer.py] => CNN HM: [0.0]
2025-07-14 17:05:39,372 [trainer.py] => CNN top1 curve: [89.81]
2025-07-14 17:05:39,372 [trainer.py] => Average Accuracy (CNN): 89.81 

2025-07-14 17:05:39,373 [trainer.py] => All params: 87988289
2025-07-14 17:05:39,374 [trainer.py] => Trainable params: 1189633
2025-07-14 17:05:39,390 [ranpac.py] => Learning on 100-110
2025-07-14 17:05:42,717 [ranpac.py] => 任务 1: 正在执行KNN稀疏校准...
2025-07-14 17:05:42,719 [ranpac.py] => [KNN校准] K值设定为: 1
2025-07-14 17:06:42,280 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 17:06:42,281 [trainer.py] => prefix: reproduce
2025-07-14 17:06:42,281 [trainer.py] => dataset: cub
2025-07-14 17:06:42,281 [trainer.py] => memory_size: 0
2025-07-14 17:06:42,281 [trainer.py] => shuffle: True
2025-07-14 17:06:42,281 [trainer.py] => init_cls: 100
2025-07-14 17:06:42,282 [trainer.py] => increment: 10
2025-07-14 17:06:42,282 [trainer.py] => model_name: ranpac
2025-07-14 17:06:42,282 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 17:06:42,282 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 17:06:42,282 [trainer.py] => seed: 1993
2025-07-14 17:06:42,282 [trainer.py] => resume: False
2025-07-14 17:06:42,282 [trainer.py] => shot: 5
2025-07-14 17:06:42,283 [trainer.py] => use_simplecil: False
2025-07-14 17:06:42,283 [trainer.py] => tuned_epoch: 1
2025-07-14 17:06:42,283 [trainer.py] => init_lr: 0.01
2025-07-14 17:06:42,283 [trainer.py] => batch_size: 48
2025-07-14 17:06:42,283 [trainer.py] => weight_decay: 0.0005
2025-07-14 17:06:42,283 [trainer.py] => min_lr: 0
2025-07-14 17:06:42,283 [trainer.py] => ffn_num: 64
2025-07-14 17:06:42,283 [trainer.py] => optimizer: sgd
2025-07-14 17:06:42,283 [trainer.py] => use_RP: True
2025-07-14 17:06:42,284 [trainer.py] => M: 10000
2025-07-14 17:06:42,284 [trainer.py] => fecam: False
2025-07-14 17:06:42,284 [trainer.py] => calibration: True
2025-07-14 17:06:42,284 [trainer.py] => knn_k: 1
2025-07-14 17:06:42,398 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 17:07:03,314 [trainer.py] => All params: 86988288
2025-07-14 17:07:03,315 [trainer.py] => Trainable params: 1189632
2025-07-14 17:07:04,961 [ranpac.py] => Learning on 0-100
2025-07-14 17:07:43,997 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 17:08:29,598 [trainer.py] => No NME accuracy.
2025-07-14 17:08:29,598 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 17:08:29,599 [trainer.py] => CNN HM: [0.0]
2025-07-14 17:08:29,599 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 17:08:29,599 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 17:08:29,601 [trainer.py] => All params: 87988289
2025-07-14 17:08:29,602 [trainer.py] => Trainable params: 1189633
2025-07-14 17:08:29,618 [ranpac.py] => Learning on 100-110
2025-07-14 17:08:32,169 [ranpac.py] => [KNN] task 1, K=1, knn_k=1
2025-07-14 17:08:53,055 [trainer.py] => No NME accuracy.
2025-07-14 17:08:53,056 [trainer.py] => CNN: {'total': 89.56, '00-99': 89.84, '100-109': 86.82, 'old': 89.84, 'new': 86.82}
2025-07-14 17:08:53,056 [trainer.py] => CNN HM: [0.0, 88.304]
2025-07-14 17:08:53,057 [trainer.py] => CNN top1 curve: [90.43, 89.56]
2025-07-14 17:08:53,057 [trainer.py] => Average Accuracy (CNN): 89.995 

2025-07-14 17:08:53,059 [trainer.py] => All params: 88088289
2025-07-14 17:08:53,061 [trainer.py] => Trainable params: 2289633
2025-07-14 17:08:53,079 [ranpac.py] => Learning on 110-120
2025-07-14 17:08:57,854 [ranpac.py] => [KNN] task 2, K=1, knn_k=1
2025-07-14 17:09:20,205 [trainer.py] => No NME accuracy.
2025-07-14 17:09:20,206 [trainer.py] => CNN: {'total': 87.73, '00-99': 89.57, '100-109': 87.16, '110-119': 69.72, 'old': 89.34, 'new': 69.72}
2025-07-14 17:09:20,207 [trainer.py] => CNN HM: [0.0, 88.304, 78.32]
2025-07-14 17:09:20,207 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73]
2025-07-14 17:09:20,207 [trainer.py] => Average Accuracy (CNN): 89.24000000000001 

2025-07-14 17:09:20,209 [trainer.py] => All params: 88188289
2025-07-14 17:09:20,211 [trainer.py] => Trainable params: 2389633
2025-07-14 17:09:20,235 [ranpac.py] => Learning on 120-130
2025-07-14 17:09:22,329 [ranpac.py] => [KNN] task 3, K=1, knn_k=1
2025-07-14 17:09:46,976 [trainer.py] => No NME accuracy.
2025-07-14 17:09:46,977 [trainer.py] => CNN: {'total': 85.86, '00-99': 88.73, '100-109': 88.18, '110-119': 71.83, '120-129': 68.62, 'old': 87.3, 'new': 68.62}
2025-07-14 17:09:46,977 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841]
2025-07-14 17:09:46,978 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86]
2025-07-14 17:09:46,978 [trainer.py] => Average Accuracy (CNN): 88.39500000000001 

2025-07-14 17:09:46,980 [trainer.py] => All params: 88288289
2025-07-14 17:09:46,981 [trainer.py] => Trainable params: 2489633
2025-07-14 17:09:47,001 [ranpac.py] => Learning on 130-140
2025-07-14 17:09:51,400 [ranpac.py] => [KNN] task 4, K=1, knn_k=1
2025-07-14 17:10:17,597 [trainer.py] => No NME accuracy.
2025-07-14 17:10:17,597 [trainer.py] => CNN: {'total': 85.27, '00-99': 88.39, '100-109': 89.19, '110-119': 72.89, '120-129': 66.21, '130-139': 81.38, 'old': 85.57, 'new': 81.38}
2025-07-14 17:10:17,598 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422]
2025-07-14 17:10:17,598 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27]
2025-07-14 17:10:17,598 [trainer.py] => Average Accuracy (CNN): 87.77000000000001 

2025-07-14 17:10:17,600 [trainer.py] => All params: 88388289
2025-07-14 17:10:17,601 [trainer.py] => Trainable params: 2589633
2025-07-14 17:10:17,620 [ranpac.py] => Learning on 140-150
2025-07-14 17:10:21,608 [ranpac.py] => [KNN] task 5, K=1, knn_k=1
2025-07-14 17:10:45,892 [trainer.py] => No NME accuracy.
2025-07-14 17:10:45,893 [trainer.py] => CNN: {'total': 83.78, '00-99': 87.87, '100-109': 90.2, '110-119': 70.77, '120-129': 67.24, '130-139': 80.69, '140-149': 68.35, 'old': 84.85, 'new': 68.35}
2025-07-14 17:10:45,893 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711]
2025-07-14 17:10:45,894 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78]
2025-07-14 17:10:45,894 [trainer.py] => Average Accuracy (CNN): 87.105 

2025-07-14 17:10:45,897 [trainer.py] => All params: 88488289
2025-07-14 17:10:45,898 [trainer.py] => Trainable params: 2689633
2025-07-14 17:10:45,917 [ranpac.py] => Learning on 150-160
2025-07-14 17:10:48,719 [ranpac.py] => [KNN] task 6, K=1, knn_k=1
2025-07-14 17:11:13,269 [trainer.py] => No NME accuracy.
2025-07-14 17:11:13,270 [trainer.py] => CNN: {'total': 83.47, '00-99': 87.73, '100-109': 89.19, '110-119': 71.48, '120-129': 67.59, '130-139': 81.38, '140-149': 67.99, '150-159': 79.79, 'old': 83.72, 'new': 79.79}
2025-07-14 17:11:13,270 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708]
2025-07-14 17:11:13,271 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47]
2025-07-14 17:11:13,271 [trainer.py] => Average Accuracy (CNN): 86.58571428571429 

2025-07-14 17:11:13,273 [trainer.py] => All params: 88588289
2025-07-14 17:11:13,274 [trainer.py] => Trainable params: 2789633
2025-07-14 17:11:13,295 [ranpac.py] => Learning on 160-170
2025-07-14 17:11:17,275 [ranpac.py] => [KNN] task 7, K=1, knn_k=1
2025-07-14 17:11:43,348 [trainer.py] => No NME accuracy.
2025-07-14 17:11:43,349 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.11, '100-109': 89.19, '110-119': 71.13, '120-129': 67.59, '130-139': 81.72, '140-149': 64.39, '150-159': 79.79, '160-169': 77.85, 'old': 82.86, 'new': 77.85}
2025-07-14 17:11:43,350 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277]
2025-07-14 17:11:43,350 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56]
2025-07-14 17:11:43,351 [trainer.py] => Average Accuracy (CNN): 86.08250000000001 

2025-07-14 17:11:43,353 [trainer.py] => All params: 88688289
2025-07-14 17:11:43,354 [trainer.py] => Trainable params: 2889633
2025-07-14 17:11:43,374 [ranpac.py] => Learning on 170-180
2025-07-14 17:11:45,557 [ranpac.py] => [KNN] task 8, K=1, knn_k=1
2025-07-14 17:12:15,980 [trainer.py] => No NME accuracy.
2025-07-14 17:12:15,981 [trainer.py] => CNN: {'total': 81.88, '00-99': 86.55, '100-109': 88.51, '110-119': 71.48, '120-129': 67.24, '130-139': 81.03, '140-149': 64.03, '150-159': 79.45, '160-169': 77.18, '170-179': 78.86, 'old': 82.07, 'new': 78.86}
2025-07-14 17:12:15,981 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433]
2025-07-14 17:12:15,982 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88]
2025-07-14 17:12:15,982 [trainer.py] => Average Accuracy (CNN): 85.61555555555556 

2025-07-14 17:12:15,984 [trainer.py] => All params: 88788289
2025-07-14 17:12:15,985 [trainer.py] => Trainable params: 2989633
2025-07-14 17:12:16,008 [ranpac.py] => Learning on 180-190
2025-07-14 17:12:18,618 [ranpac.py] => [KNN] task 9, K=1, knn_k=1
2025-07-14 17:12:48,035 [trainer.py] => No NME accuracy.
2025-07-14 17:12:48,035 [trainer.py] => CNN: {'total': 81.03, '00-99': 85.79, '100-109': 88.85, '110-119': 72.18, '120-129': 67.93, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.29, 'new': 76.31}
2025-07-14 17:12:48,036 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721]
2025-07-14 17:12:48,037 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03]
2025-07-14 17:12:48,037 [trainer.py] => Average Accuracy (CNN): 85.15700000000001 

2025-07-14 17:12:48,039 [trainer.py] => All params: 88888289
2025-07-14 17:12:48,041 [trainer.py] => Trainable params: 3089633
2025-07-14 17:12:48,065 [ranpac.py] => Learning on 190-200
2025-07-14 17:12:50,844 [ranpac.py] => [KNN] task 10, K=1, knn_k=1
2025-07-14 17:13:19,868 [trainer.py] => No NME accuracy.
2025-07-14 17:13:19,868 [trainer.py] => CNN: {'total': 80.17, '00-99': 84.92, '100-109': 88.51, '110-119': 73.59, '120-129': 68.28, '130-139': 77.59, '140-149': 62.95, '150-159': 79.45, '160-169': 76.85, '170-179': 78.52, '180-189': 74.91, '190-199': 72.97, 'old': 80.56, 'new': 72.97}
2025-07-14 17:13:19,869 [trainer.py] => CNN HM: [0.0, 88.304, 78.32, 76.841, 83.422, 75.711, 81.708, 80.277, 80.433, 78.721, 76.577]
2025-07-14 17:13:19,870 [trainer.py] => CNN top1 curve: [90.43, 89.56, 87.73, 85.86, 85.27, 83.78, 83.47, 82.56, 81.88, 81.03, 80.17]
2025-07-14 17:13:19,870 [trainer.py] => Average Accuracy (CNN): 84.70363636363636 

2025-07-14 17:13:19,872 [trainer.py] => Forgetting (CNN): 2.0150000000000006
2025-07-14 17:54:29,237 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 17:54:29,239 [trainer.py] => prefix: reproduce
2025-07-14 17:54:29,240 [trainer.py] => dataset: cub
2025-07-14 17:54:29,240 [trainer.py] => memory_size: 0
2025-07-14 17:54:29,240 [trainer.py] => shuffle: True
2025-07-14 17:54:29,240 [trainer.py] => init_cls: 100
2025-07-14 17:54:29,241 [trainer.py] => increment: 10
2025-07-14 17:54:29,241 [trainer.py] => model_name: ranpac
2025-07-14 17:54:29,241 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 17:54:29,241 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 17:54:29,241 [trainer.py] => seed: 1993
2025-07-14 17:54:29,241 [trainer.py] => resume: False
2025-07-14 17:54:29,241 [trainer.py] => shot: 5
2025-07-14 17:54:29,241 [trainer.py] => use_simplecil: False
2025-07-14 17:54:29,242 [trainer.py] => tuned_epoch: 1
2025-07-14 17:54:29,242 [trainer.py] => init_lr: 0.01
2025-07-14 17:54:29,242 [trainer.py] => batch_size: 48
2025-07-14 17:54:29,242 [trainer.py] => weight_decay: 0.0005
2025-07-14 17:54:29,242 [trainer.py] => min_lr: 0
2025-07-14 17:54:29,242 [trainer.py] => ffn_num: 64
2025-07-14 17:54:29,242 [trainer.py] => optimizer: sgd
2025-07-14 17:54:29,242 [trainer.py] => use_RP: True
2025-07-14 17:54:29,243 [trainer.py] => M: 10000
2025-07-14 17:54:29,243 [trainer.py] => fecam: False
2025-07-14 17:54:29,243 [trainer.py] => calibration: True
2025-07-14 17:54:29,243 [trainer.py] => knn_k: 5
2025-07-14 17:54:29,243 [trainer.py] => knn_distance_metric: cosine
2025-07-14 17:54:29,243 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 17:54:29,243 [trainer.py] => knn_adaptive_k: False
2025-07-14 17:54:29,243 [trainer.py] => knn_temperature: 16.0
2025-07-14 17:54:29,382 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 17:54:54,850 [trainer.py] => All params: 86988288
2025-07-14 17:54:54,851 [trainer.py] => Trainable params: 1189632
2025-07-14 17:54:56,637 [ranpac.py] => Learning on 0-100
2025-07-14 17:55:37,666 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 17:56:03,490 [trainer.py] => No NME accuracy.
2025-07-14 17:56:03,490 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 17:56:03,490 [trainer.py] => CNN HM: [0.0]
2025-07-14 17:56:03,490 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 17:56:03,490 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 17:56:03,491 [trainer.py] => All params: 87988289
2025-07-14 17:56:03,492 [trainer.py] => Trainable params: 1189633
2025-07-14 17:56:03,504 [ranpac.py] => Learning on 100-110
2025-07-14 17:56:06,119 [ranpac.py] => [KNN] task 1, fixed K=5
2025-07-14 17:56:06,121 [ranpac.py] => [KNN] task 1, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:56:20,312 [trainer.py] => No NME accuracy.
2025-07-14 17:56:20,313 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.88, '100-109': 87.16, 'old': 89.88, 'new': 87.16}
2025-07-14 17:56:20,313 [trainer.py] => CNN HM: [0.0, 88.499]
2025-07-14 17:56:20,313 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-14 17:56:20,313 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-14 17:56:20,315 [trainer.py] => All params: 88088289
2025-07-14 17:56:20,316 [trainer.py] => Trainable params: 2289633
2025-07-14 17:56:20,330 [ranpac.py] => Learning on 110-120
2025-07-14 17:56:23,007 [ranpac.py] => [KNN] task 2, fixed K=5
2025-07-14 17:56:23,008 [ranpac.py] => [KNN] task 2, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:56:38,069 [trainer.py] => No NME accuracy.
2025-07-14 17:56:38,069 [trainer.py] => CNN: {'total': 87.76, '00-99': 89.29, '100-109': 88.18, '110-119': 71.83, 'old': 89.19, 'new': 71.83}
2025-07-14 17:56:38,069 [trainer.py] => CNN HM: [0.0, 88.499, 79.574]
2025-07-14 17:56:38,070 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76]
2025-07-14 17:56:38,070 [trainer.py] => Average Accuracy (CNN): 89.27333333333333 

2025-07-14 17:56:38,071 [trainer.py] => All params: 88188289
2025-07-14 17:56:38,072 [trainer.py] => Trainable params: 2389633
2025-07-14 17:56:38,100 [ranpac.py] => Learning on 120-130
2025-07-14 17:56:41,121 [ranpac.py] => [KNN] task 3, fixed K=5
2025-07-14 17:56:41,122 [ranpac.py] => [KNN] task 3, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:56:57,328 [trainer.py] => No NME accuracy.
2025-07-14 17:56:57,328 [trainer.py] => CNN: {'total': 86.21, '00-99': 88.67, '100-109': 89.53, '110-119': 74.3, '120-129': 70.0, 'old': 87.56, 'new': 70.0}
2025-07-14 17:56:57,328 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801]
2025-07-14 17:56:57,329 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21]
2025-07-14 17:56:57,329 [trainer.py] => Average Accuracy (CNN): 88.5075 

2025-07-14 17:56:57,330 [trainer.py] => All params: 88288289
2025-07-14 17:56:57,331 [trainer.py] => Trainable params: 2489633
2025-07-14 17:56:57,349 [ranpac.py] => Learning on 130-140
2025-07-14 17:56:59,876 [ranpac.py] => [KNN] task 4, fixed K=5
2025-07-14 17:56:59,877 [ranpac.py] => [KNN] task 4, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:57:16,209 [trainer.py] => No NME accuracy.
2025-07-14 17:57:16,209 [trainer.py] => CNN: {'total': 85.56, '00-99': 88.25, '100-109': 89.86, '110-119': 74.3, '120-129': 68.28, '130-139': 82.76, 'old': 85.78, 'new': 82.76}
2025-07-14 17:57:16,209 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243]
2025-07-14 17:57:16,209 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56]
2025-07-14 17:57:16,210 [trainer.py] => Average Accuracy (CNN): 87.91799999999999 

2025-07-14 17:57:16,210 [trainer.py] => All params: 88388289
2025-07-14 17:57:16,211 [trainer.py] => Trainable params: 2589633
2025-07-14 17:57:16,229 [ranpac.py] => Learning on 140-150
2025-07-14 17:57:18,560 [ranpac.py] => [KNN] task 5, fixed K=5
2025-07-14 17:57:18,561 [ranpac.py] => [KNN] task 5, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:57:36,957 [trainer.py] => No NME accuracy.
2025-07-14 17:57:36,958 [trainer.py] => CNN: {'total': 84.2, '00-99': 87.83, '100-109': 90.54, '110-119': 71.83, '120-129': 68.28, '130-139': 83.45, '140-149': 69.78, 'old': 85.19, 'new': 69.78}
2025-07-14 17:57:36,959 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719]
2025-07-14 17:57:36,959 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2]
2025-07-14 17:57:36,960 [trainer.py] => Average Accuracy (CNN): 87.29833333333333 

2025-07-14 17:57:36,962 [trainer.py] => All params: 88488289
2025-07-14 17:57:36,963 [trainer.py] => Trainable params: 2689633
2025-07-14 17:57:36,989 [ranpac.py] => Learning on 150-160
2025-07-14 17:57:40,009 [ranpac.py] => [KNN] task 6, fixed K=5
2025-07-14 17:57:40,012 [ranpac.py] => [KNN] task 6, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:57:59,893 [trainer.py] => No NME accuracy.
2025-07-14 17:57:59,894 [trainer.py] => CNN: {'total': 83.86, '00-99': 87.45, '100-109': 88.18, '110-119': 73.24, '120-129': 69.31, '130-139': 83.79, '140-149': 70.5, '150-159': 81.51, 'old': 84.02, 'new': 81.51}
2025-07-14 17:57:59,895 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719, 82.746]
2025-07-14 17:57:59,896 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2, 83.86]
2025-07-14 17:57:59,896 [trainer.py] => Average Accuracy (CNN): 86.80714285714285 

2025-07-14 17:57:59,897 [trainer.py] => All params: 88588289
2025-07-14 17:57:59,898 [trainer.py] => Trainable params: 2789633
2025-07-14 17:57:59,916 [ranpac.py] => Learning on 160-170
2025-07-14 17:58:02,299 [ranpac.py] => [KNN] task 7, fixed K=5
2025-07-14 17:58:02,300 [ranpac.py] => [KNN] task 7, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:58:21,865 [trainer.py] => No NME accuracy.
2025-07-14 17:58:21,865 [trainer.py] => CNN: {'total': 83.21, '00-99': 87.11, '100-109': 88.51, '110-119': 73.94, '120-129': 70.34, '130-139': 82.41, '140-149': 66.91, '150-159': 80.48, '160-169': 80.2, 'old': 83.4, 'new': 80.2}
2025-07-14 17:58:21,865 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719, 82.746, 81.769]
2025-07-14 17:58:21,865 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2, 83.86, 83.21]
2025-07-14 17:58:21,865 [trainer.py] => Average Accuracy (CNN): 86.3575 

2025-07-14 17:58:21,866 [trainer.py] => All params: 88688289
2025-07-14 17:58:21,866 [trainer.py] => Trainable params: 2889633
2025-07-14 17:58:21,883 [ranpac.py] => Learning on 170-180
2025-07-14 17:58:24,342 [ranpac.py] => [KNN] task 8, fixed K=5
2025-07-14 17:58:24,343 [ranpac.py] => [KNN] task 8, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:58:46,673 [trainer.py] => No NME accuracy.
2025-07-14 17:58:46,674 [trainer.py] => CNN: {'total': 82.33, '00-99': 86.2, '100-109': 88.18, '110-119': 73.94, '120-129': 70.34, '130-139': 81.72, '140-149': 65.47, '150-159': 80.48, '160-169': 79.53, '170-179': 79.53, 'old': 82.5, 'new': 79.53}
2025-07-14 17:58:46,675 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719, 82.746, 81.769, 80.988]
2025-07-14 17:58:46,675 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2, 83.86, 83.21, 82.33]
2025-07-14 17:58:46,675 [trainer.py] => Average Accuracy (CNN): 85.91000000000001 

2025-07-14 17:58:46,677 [trainer.py] => All params: 88788289
2025-07-14 17:58:46,678 [trainer.py] => Trainable params: 2989633
2025-07-14 17:58:46,699 [ranpac.py] => Learning on 180-190
2025-07-14 17:58:49,255 [ranpac.py] => [KNN] task 9, fixed K=5
2025-07-14 17:58:49,256 [ranpac.py] => [KNN] task 9, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:59:11,857 [trainer.py] => No NME accuracy.
2025-07-14 17:59:11,857 [trainer.py] => CNN: {'total': 81.72, '00-99': 85.89, '100-109': 87.84, '110-119': 74.3, '120-129': 69.66, '130-139': 80.0, '140-149': 64.39, '150-159': 80.82, '160-169': 79.53, '170-179': 79.87, '180-189': 76.66, 'old': 82.0, 'new': 76.66}
2025-07-14 17:59:11,858 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719, 82.746, 81.769, 80.988, 79.24]
2025-07-14 17:59:11,858 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2, 83.86, 83.21, 82.33, 81.72]
2025-07-14 17:59:11,858 [trainer.py] => Average Accuracy (CNN): 85.49100000000001 

2025-07-14 17:59:11,859 [trainer.py] => All params: 88888289
2025-07-14 17:59:11,861 [trainer.py] => Trainable params: 3089633
2025-07-14 17:59:11,884 [ranpac.py] => Learning on 190-200
2025-07-14 17:59:14,164 [ranpac.py] => [KNN] task 10, fixed K=5
2025-07-14 17:59:14,165 [ranpac.py] => [KNN] task 10, weight sparsity: 0.950, distance_metric: cosine
2025-07-14 17:59:36,787 [trainer.py] => No NME accuracy.
2025-07-14 17:59:36,787 [trainer.py] => CNN: {'total': 80.81, '00-99': 85.03, '100-109': 88.85, '110-119': 73.59, '120-129': 68.62, '130-139': 81.03, '140-149': 64.75, '150-159': 81.16, '160-169': 78.86, '170-179': 79.19, '180-189': 75.96, '190-199': 73.31, 'old': 81.21, 'new': 73.31}
2025-07-14 17:59:36,788 [trainer.py] => CNN HM: [0.0, 88.499, 79.574, 77.801, 84.243, 76.719, 82.746, 81.769, 80.988, 79.24, 77.058]
2025-07-14 17:59:36,790 [trainer.py] => CNN top1 curve: [90.43, 89.63, 87.76, 86.21, 85.56, 84.2, 83.86, 83.21, 82.33, 81.72, 80.81]
2025-07-14 17:59:36,790 [trainer.py] => Average Accuracy (CNN): 85.06545454545454 

2025-07-14 17:59:36,791 [trainer.py] => Forgetting (CNN): 2.110000000000004
2025-07-14 21:07:44,464 [trainer.py] => config: ./exps/ranpac.json
2025-07-14 21:07:44,465 [trainer.py] => prefix: reproduce
2025-07-14 21:07:44,465 [trainer.py] => dataset: cub
2025-07-14 21:07:44,466 [trainer.py] => memory_size: 0
2025-07-14 21:07:44,466 [trainer.py] => shuffle: True
2025-07-14 21:07:44,467 [trainer.py] => init_cls: 100
2025-07-14 21:07:44,467 [trainer.py] => increment: 10
2025-07-14 21:07:44,467 [trainer.py] => model_name: ranpac
2025-07-14 21:07:44,468 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 21:07:44,468 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 21:07:44,468 [trainer.py] => seed: 1993
2025-07-14 21:07:44,469 [trainer.py] => resume: False
2025-07-14 21:07:44,469 [trainer.py] => shot: 5
2025-07-14 21:07:44,470 [trainer.py] => use_simplecil: False
2025-07-14 21:07:44,470 [trainer.py] => tuned_epoch: 1
2025-07-14 21:07:44,470 [trainer.py] => init_lr: 0.01
2025-07-14 21:07:44,471 [trainer.py] => batch_size: 48
2025-07-14 21:07:44,471 [trainer.py] => weight_decay: 0.0005
2025-07-14 21:07:44,471 [trainer.py] => min_lr: 0
2025-07-14 21:07:44,472 [trainer.py] => ffn_num: 64
2025-07-14 21:07:44,472 [trainer.py] => optimizer: sgd
2025-07-14 21:07:44,472 [trainer.py] => use_RP: True
2025-07-14 21:07:44,473 [trainer.py] => M: 10000
2025-07-14 21:07:44,473 [trainer.py] => fecam: False
2025-07-14 21:07:44,473 [trainer.py] => calibration: True
2025-07-14 21:07:44,474 [trainer.py] => knn_k: 5
2025-07-14 21:07:44,474 [trainer.py] => knn_distance_metric: cosine
2025-07-14 21:07:44,474 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 21:07:44,475 [trainer.py] => knn_adaptive_k: True
2025-07-14 21:07:44,475 [trainer.py] => knn_temperature: 16.0
2025-07-14 21:07:44,475 [trainer.py] => k_min: 3
2025-07-14 21:07:44,475 [trainer.py] => k_max: 21
2025-07-14 21:07:44,476 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 21:07:44,476 [trainer.py] => cosine_temperature: 16.0
2025-07-14 21:07:44,599 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 21:08:11,503 [trainer.py] => All params: 86988288
2025-07-14 21:08:11,504 [trainer.py] => Trainable params: 1189632
2025-07-14 21:08:13,134 [ranpac.py] => Learning on 0-100
2025-07-14 21:08:57,508 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 21:09:25,106 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 21:09:25,648 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-14 21:09:25,649 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 21:09:25,649 [trainer.py] => No NME accuracy.
2025-07-14 21:09:25,649 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 21:09:25,649 [trainer.py] => CNN HM: [0.0]
2025-07-14 21:09:25,649 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 21:09:25,649 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 21:09:25,650 [trainer.py] => All params: 87988289
2025-07-14 21:09:25,650 [trainer.py] => Trainable params: 1189633
2025-07-14 21:09:25,661 [ranpac.py] => Learning on 100-110
2025-07-14 21:09:29,982 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:09:30,013 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 21:09:30,014 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 21:09:30,016 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-14 21:09:43,851 [trainer.py] => No NME accuracy.
2025-07-14 21:09:43,851 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-14 21:09:43,851 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-14 21:09:43,851 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-14 21:09:43,852 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-14 21:09:43,853 [trainer.py] => All params: 88088289
2025-07-14 21:09:43,855 [trainer.py] => Trainable params: 2289633
2025-07-14 21:09:43,873 [ranpac.py] => Learning on 110-120
2025-07-14 21:09:46,313 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:09:46,315 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-14 21:09:46,315 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-14 21:09:46,316 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-14 21:10:00,410 [trainer.py] => No NME accuracy.
2025-07-14 21:10:00,410 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-14 21:10:00,410 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-14 21:10:00,410 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-14 21:10:00,411 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-14 21:10:00,413 [trainer.py] => All params: 88188289
2025-07-14 21:10:00,414 [trainer.py] => Trainable params: 2389633
2025-07-14 21:10:00,426 [ranpac.py] => Learning on 120-130
2025-07-14 21:10:03,576 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:10:03,578 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-14 21:10:03,578 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-14 21:10:03,579 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-14 21:10:18,594 [trainer.py] => No NME accuracy.
2025-07-14 21:10:18,594 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.87, '100-109': 89.53, '110-119': 73.59, '120-129': 71.03, 'old': 87.68, 'new': 71.03}
2025-07-14 21:10:18,594 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482]
2025-07-14 21:10:18,594 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39]
2025-07-14 21:10:18,594 [trainer.py] => Average Accuracy (CNN): 88.64 

2025-07-14 21:10:18,595 [trainer.py] => All params: 88288289
2025-07-14 21:10:18,596 [trainer.py] => Trainable params: 2489633
2025-07-14 21:10:18,611 [ranpac.py] => Learning on 130-140
2025-07-14 21:10:21,830 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:10:21,832 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-14 21:10:21,833 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-14 21:10:21,833 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-14 21:10:37,877 [trainer.py] => No NME accuracy.
2025-07-14 21:10:37,878 [trainer.py] => CNN: {'total': 85.74, '00-99': 88.77, '100-109': 89.86, '110-119': 73.59, '120-129': 67.59, '130-139': 81.38, 'old': 86.07, 'new': 81.38}
2025-07-14 21:10:37,878 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659]
2025-07-14 21:10:37,878 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74]
2025-07-14 21:10:37,879 [trainer.py] => Average Accuracy (CNN): 88.06 

2025-07-14 21:10:37,880 [trainer.py] => All params: 88388289
2025-07-14 21:10:37,882 [trainer.py] => Trainable params: 2589633
2025-07-14 21:10:37,899 [ranpac.py] => Learning on 140-150
2025-07-14 21:10:41,341 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:10:41,343 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-14 21:10:41,343 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-14 21:10:41,344 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: cosine
2025-07-14 21:10:58,142 [trainer.py] => No NME accuracy.
2025-07-14 21:10:58,143 [trainer.py] => CNN: {'total': 84.25, '00-99': 87.9, '100-109': 90.54, '110-119': 71.83, '120-129': 69.66, '130-139': 82.07, '140-149': 69.78, 'old': 85.24, 'new': 69.78}
2025-07-14 21:10:58,143 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739]
2025-07-14 21:10:58,143 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25]
2025-07-14 21:10:58,143 [trainer.py] => Average Accuracy (CNN): 87.425 

2025-07-14 21:10:58,145 [trainer.py] => All params: 88488289
2025-07-14 21:10:58,147 [trainer.py] => Trainable params: 2689633
2025-07-14 21:10:58,164 [ranpac.py] => Learning on 150-160
2025-07-14 21:11:00,898 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:11:00,900 [ranpac.py] => [Dynamic-K] Computed K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-14 21:11:00,900 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-14 21:11:00,901 [ranpac.py] => [KNN] task 6, weight sparsity: 0.910, distance_metric: cosine
2025-07-14 21:11:18,462 [trainer.py] => No NME accuracy.
2025-07-14 21:11:18,462 [trainer.py] => CNN: {'total': 83.92, '00-99': 87.63, '100-109': 88.85, '110-119': 73.59, '120-129': 68.97, '130-139': 82.76, '140-149': 70.14, '150-159': 81.51, 'old': 84.09, 'new': 81.51}
2025-07-14 21:11:18,462 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78]
2025-07-14 21:11:18,463 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92]
2025-07-14 21:11:18,463 [trainer.py] => Average Accuracy (CNN): 86.9242857142857 

2025-07-14 21:11:18,465 [trainer.py] => All params: 88588289
2025-07-14 21:11:18,466 [trainer.py] => Trainable params: 2789633
2025-07-14 21:11:18,485 [ranpac.py] => Learning on 160-170
2025-07-14 21:11:21,012 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:11:21,014 [ranpac.py] => [Dynamic-K] Computed K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-14 21:11:21,014 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-14 21:11:21,015 [ranpac.py] => [KNN] task 7, weight sparsity: 0.904, distance_metric: cosine
2025-07-14 21:11:40,127 [trainer.py] => No NME accuracy.
2025-07-14 21:11:40,127 [trainer.py] => CNN: {'total': 83.11, '00-99': 87.07, '100-109': 88.51, '110-119': 74.3, '120-129': 69.66, '130-139': 81.03, '140-149': 66.19, '150-159': 81.16, '160-169': 80.54, 'old': 83.27, 'new': 80.54}
2025-07-14 21:11:40,127 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882]
2025-07-14 21:11:40,127 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11]
2025-07-14 21:11:40,127 [trainer.py] => Average Accuracy (CNN): 86.44749999999999 

2025-07-14 21:11:40,128 [trainer.py] => All params: 88688289
2025-07-14 21:11:40,129 [trainer.py] => Trainable params: 2889633
2025-07-14 21:11:40,150 [ranpac.py] => Learning on 170-180
2025-07-14 21:11:43,509 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:11:43,512 [ranpac.py] => [Dynamic-K] Computed K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-14 21:11:43,512 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-14 21:11:43,513 [ranpac.py] => [KNN] task 8, weight sparsity: 0.915, distance_metric: cosine
2025-07-14 21:12:05,197 [trainer.py] => No NME accuracy.
2025-07-14 21:12:05,197 [trainer.py] => CNN: {'total': 82.29, '00-99': 86.27, '100-109': 88.18, '110-119': 73.59, '120-129': 69.31, '130-139': 81.38, '140-149': 65.11, '150-159': 80.82, '160-169': 79.53, '170-179': 79.87, 'old': 82.43, 'new': 79.87}
2025-07-14 21:12:05,198 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13]
2025-07-14 21:12:05,198 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29]
2025-07-14 21:12:05,198 [trainer.py] => Average Accuracy (CNN): 85.98555555555555 

2025-07-14 21:12:05,199 [trainer.py] => All params: 88788289
2025-07-14 21:12:05,199 [trainer.py] => Trainable params: 2989633
2025-07-14 21:12:05,227 [ranpac.py] => Learning on 180-190
2025-07-14 21:12:07,790 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:12:07,791 [ranpac.py] => [Dynamic-K] Computed K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-14 21:12:07,792 [ranpac.py] => [KNN] task 9, dynamic K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-14 21:12:07,792 [ranpac.py] => [KNN] task 9, weight sparsity: 0.887, distance_metric: cosine
2025-07-14 21:12:30,307 [trainer.py] => No NME accuracy.
2025-07-14 21:12:30,307 [trainer.py] => CNN: {'total': 81.58, '00-99': 85.93, '100-109': 87.84, '110-119': 72.89, '120-129': 68.97, '130-139': 79.66, '140-149': 65.11, '150-159': 80.48, '160-169': 78.52, '170-179': 79.19, '180-189': 77.35, 'old': 81.81, 'new': 77.35}
2025-07-14 21:12:30,307 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13, 79.518]
2025-07-14 21:12:30,307 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29, 81.58]
2025-07-14 21:12:30,308 [trainer.py] => Average Accuracy (CNN): 85.54499999999999 

2025-07-14 21:12:30,309 [trainer.py] => All params: 88888289
2025-07-14 21:12:30,310 [trainer.py] => Trainable params: 3089633
2025-07-14 21:12:30,331 [ranpac.py] => Learning on 190-200
2025-07-14 21:12:33,447 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:12:33,449 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-14 21:12:33,449 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-14 21:12:33,450 [ranpac.py] => [KNN] task 10, weight sparsity: 0.918, distance_metric: cosine
2025-07-14 21:12:55,903 [trainer.py] => No NME accuracy.
2025-07-14 21:12:55,904 [trainer.py] => CNN: {'total': 80.77, '00-99': 85.1, '100-109': 88.18, '110-119': 73.94, '120-129': 68.62, '130-139': 80.34, '140-149': 64.75, '150-159': 81.16, '160-169': 78.19, '170-179': 78.52, '180-189': 77.0, '190-199': 73.31, 'old': 81.17, 'new': 73.31}
2025-07-14 21:12:55,904 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13, 79.518, 77.04]
2025-07-14 21:12:55,905 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29, 81.58, 80.77]
2025-07-14 21:12:55,905 [trainer.py] => Average Accuracy (CNN): 85.11090909090909 

2025-07-14 21:12:55,906 [trainer.py] => Forgetting (CNN): 2.267000000000003
2025-07-14 21:28:48,347 [trainer.py] => config: ./exps/ranpac_mahalanobis.json
2025-07-14 21:28:48,348 [trainer.py] => prefix: reproduce
2025-07-14 21:28:48,348 [trainer.py] => dataset: cub
2025-07-14 21:28:48,349 [trainer.py] => memory_size: 0
2025-07-14 21:28:48,349 [trainer.py] => shuffle: True
2025-07-14 21:28:48,350 [trainer.py] => init_cls: 100
2025-07-14 21:28:48,351 [trainer.py] => increment: 10
2025-07-14 21:28:48,351 [trainer.py] => model_name: ranpac
2025-07-14 21:28:48,351 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-14 21:28:48,352 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-14 21:28:48,352 [trainer.py] => seed: 1993
2025-07-14 21:28:48,353 [trainer.py] => resume: False
2025-07-14 21:28:48,353 [trainer.py] => shot: 5
2025-07-14 21:28:48,354 [trainer.py] => use_simplecil: False
2025-07-14 21:28:48,354 [trainer.py] => tuned_epoch: 1
2025-07-14 21:28:48,355 [trainer.py] => init_lr: 0.01
2025-07-14 21:28:48,355 [trainer.py] => batch_size: 48
2025-07-14 21:28:48,355 [trainer.py] => weight_decay: 0.0005
2025-07-14 21:28:48,355 [trainer.py] => min_lr: 0
2025-07-14 21:28:48,355 [trainer.py] => ffn_num: 64
2025-07-14 21:28:48,355 [trainer.py] => optimizer: sgd
2025-07-14 21:28:48,355 [trainer.py] => use_RP: True
2025-07-14 21:28:48,355 [trainer.py] => M: 10000
2025-07-14 21:28:48,356 [trainer.py] => fecam: False
2025-07-14 21:28:48,356 [trainer.py] => calibration: True
2025-07-14 21:28:48,356 [trainer.py] => knn_k: 5
2025-07-14 21:28:48,356 [trainer.py] => knn_distance_metric: mahalanobis
2025-07-14 21:28:48,356 [trainer.py] => knn_weight_decay: 0.1
2025-07-14 21:28:48,356 [trainer.py] => knn_adaptive_k: True
2025-07-14 21:28:48,356 [trainer.py] => knn_temperature: 16.0
2025-07-14 21:28:48,356 [trainer.py] => k_min: 3
2025-07-14 21:28:48,356 [trainer.py] => k_max: 21
2025-07-14 21:28:48,357 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-14 21:28:48,357 [trainer.py] => cosine_temperature: 16.0
2025-07-14 21:28:48,482 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-14 21:29:18,537 [trainer.py] => All params: 86988288
2025-07-14 21:29:18,538 [trainer.py] => Trainable params: 1189632
2025-07-14 21:29:20,139 [ranpac.py] => Learning on 0-100
2025-07-14 21:30:00,445 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-14 21:30:41,440 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-14 21:30:42,102 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-14 21:30:42,102 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-14 21:30:42,102 [trainer.py] => No NME accuracy.
2025-07-14 21:30:42,102 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-14 21:30:42,102 [trainer.py] => CNN HM: [0.0]
2025-07-14 21:30:42,102 [trainer.py] => CNN top1 curve: [90.43]
2025-07-14 21:30:42,102 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-14 21:30:42,103 [trainer.py] => All params: 87988289
2025-07-14 21:30:42,104 [trainer.py] => Trainable params: 1189633
2025-07-14 21:30:42,116 [ranpac.py] => Learning on 100-110
2025-07-14 21:31:32,234 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:31:32,236 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 21:31:32,237 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-14 21:31:32,238 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: mahalanobis
2025-07-14 21:31:56,844 [trainer.py] => No NME accuracy.
2025-07-14 21:31:56,845 [trainer.py] => CNN: {'total': 89.72, '00-99': 89.91, '100-109': 87.84, 'old': 89.91, 'new': 87.84}
2025-07-14 21:31:56,846 [trainer.py] => CNN HM: [0.0, 88.863]
2025-07-14 21:31:56,846 [trainer.py] => CNN top1 curve: [90.43, 89.72]
2025-07-14 21:31:56,847 [trainer.py] => Average Accuracy (CNN): 90.075 

2025-07-14 21:31:56,849 [trainer.py] => All params: 88088289
2025-07-14 21:31:56,852 [trainer.py] => Trainable params: 2289633
2025-07-14 21:31:56,869 [ranpac.py] => Learning on 110-120
2025-07-14 21:32:43,808 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:32:43,810 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-14 21:32:43,811 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-14 21:32:43,812 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: mahalanobis
2025-07-14 21:33:08,648 [trainer.py] => No NME accuracy.
2025-07-14 21:33:08,649 [trainer.py] => CNN: {'total': 88.14, '00-99': 89.88, '100-109': 87.5, '110-119': 71.13, 'old': 89.66, 'new': 71.13}
2025-07-14 21:33:08,649 [trainer.py] => CNN HM: [0.0, 88.863, 79.327]
2025-07-14 21:33:08,649 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14]
2025-07-14 21:33:08,649 [trainer.py] => Average Accuracy (CNN): 89.43 

2025-07-14 21:33:08,651 [trainer.py] => All params: 88188289
2025-07-14 21:33:08,653 [trainer.py] => Trainable params: 2389633
2025-07-14 21:33:08,668 [ranpac.py] => Learning on 120-130
2025-07-14 21:33:54,514 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:33:54,516 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-14 21:33:54,516 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-14 21:33:54,517 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: mahalanobis
2025-07-14 21:34:15,953 [trainer.py] => No NME accuracy.
2025-07-14 21:34:15,953 [trainer.py] => CNN: {'total': 86.15, '00-99': 89.19, '100-109': 88.18, '110-119': 71.48, '120-129': 68.28, 'old': 87.65, 'new': 68.28}
2025-07-14 21:34:15,953 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762]
2025-07-14 21:34:15,953 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15]
2025-07-14 21:34:15,954 [trainer.py] => Average Accuracy (CNN): 88.61000000000001 

2025-07-14 21:34:15,955 [trainer.py] => All params: 88288289
2025-07-14 21:34:15,956 [trainer.py] => Trainable params: 2489633
2025-07-14 21:34:15,973 [ranpac.py] => Learning on 130-140
2025-07-14 21:35:03,030 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:35:03,032 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-14 21:35:03,032 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-14 21:35:03,033 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: mahalanobis
2025-07-14 21:35:28,096 [trainer.py] => No NME accuracy.
2025-07-14 21:35:28,097 [trainer.py] => CNN: {'total': 85.46, '00-99': 88.6, '100-109': 88.85, '110-119': 72.89, '120-129': 65.52, '130-139': 83.1, 'old': 85.65, 'new': 83.1}
2025-07-14 21:35:28,097 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356]
2025-07-14 21:35:28,097 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46]
2025-07-14 21:35:28,097 [trainer.py] => Average Accuracy (CNN): 87.98 

2025-07-14 21:35:28,098 [trainer.py] => All params: 88388289
2025-07-14 21:35:28,100 [trainer.py] => Trainable params: 2589633
2025-07-14 21:35:28,118 [ranpac.py] => Learning on 140-150
2025-07-14 21:36:15,135 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:36:15,137 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-14 21:36:15,138 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-14 21:36:15,139 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: mahalanobis
2025-07-14 21:36:44,887 [trainer.py] => No NME accuracy.
2025-07-14 21:36:44,888 [trainer.py] => CNN: {'total': 83.95, '00-99': 88.11, '100-109': 89.19, '110-119': 71.48, '120-129': 64.83, '130-139': 83.1, '140-149': 68.71, 'old': 84.99, 'new': 68.71}
2025-07-14 21:36:44,889 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988]
2025-07-14 21:36:44,889 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95]
2025-07-14 21:36:44,891 [trainer.py] => Average Accuracy (CNN): 87.30833333333334 

2025-07-14 21:36:44,893 [trainer.py] => All params: 88488289
2025-07-14 21:36:44,894 [trainer.py] => Trainable params: 2689633
2025-07-14 21:36:44,916 [ranpac.py] => Learning on 150-160
2025-07-14 21:37:32,356 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:37:32,358 [ranpac.py] => [Dynamic-K] Computed K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-14 21:37:32,358 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-14 21:37:32,359 [ranpac.py] => [KNN] task 6, weight sparsity: 0.910, distance_metric: mahalanobis
2025-07-14 21:38:01,343 [trainer.py] => No NME accuracy.
2025-07-14 21:38:01,344 [trainer.py] => CNN: {'total': 83.34, '00-99': 87.66, '100-109': 87.84, '110-119': 72.54, '120-129': 66.21, '130-139': 83.1, '140-149': 68.35, '150-159': 78.08, 'old': 83.69, 'new': 78.08}
2025-07-14 21:38:01,344 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988, 80.788]
2025-07-14 21:38:01,345 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95, 83.34]
2025-07-14 21:38:01,345 [trainer.py] => Average Accuracy (CNN): 86.74142857142859 

2025-07-14 21:38:01,347 [trainer.py] => All params: 88588289
2025-07-14 21:38:01,348 [trainer.py] => Trainable params: 2789633
2025-07-14 21:38:01,369 [ranpac.py] => Learning on 160-170
2025-07-14 21:38:49,354 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:38:49,356 [ranpac.py] => [Dynamic-K] Computed K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-14 21:38:49,357 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-14 21:38:49,358 [ranpac.py] => [KNN] task 7, weight sparsity: 0.904, distance_metric: mahalanobis
2025-07-14 21:39:21,122 [trainer.py] => No NME accuracy.
2025-07-14 21:39:21,123 [trainer.py] => CNN: {'total': 82.56, '00-99': 87.14, '100-109': 88.51, '110-119': 72.54, '120-129': 66.21, '130-139': 81.72, '140-149': 64.75, '150-159': 79.45, '160-169': 78.19, 'old': 82.84, 'new': 78.19}
2025-07-14 21:39:21,124 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988, 80.788, 80.448]
2025-07-14 21:39:21,124 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95, 83.34, 82.56]
2025-07-14 21:39:21,124 [trainer.py] => Average Accuracy (CNN): 86.21875 

2025-07-14 21:39:21,126 [trainer.py] => All params: 88688289
2025-07-14 21:39:21,127 [trainer.py] => Trainable params: 2889633
2025-07-14 21:39:21,146 [ranpac.py] => Learning on 170-180
2025-07-14 21:40:08,716 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:40:08,719 [ranpac.py] => [Dynamic-K] Computed K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-14 21:40:08,719 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-14 21:40:08,720 [ranpac.py] => [KNN] task 8, weight sparsity: 0.915, distance_metric: mahalanobis
2025-07-14 21:40:43,895 [trainer.py] => No NME accuracy.
2025-07-14 21:40:43,895 [trainer.py] => CNN: {'total': 81.9, '00-99': 86.59, '100-109': 87.84, '110-119': 71.83, '120-129': 66.55, '130-139': 82.07, '140-149': 61.87, '150-159': 79.79, '160-169': 78.19, '170-179': 79.53, 'old': 82.05, 'new': 79.53}
2025-07-14 21:40:43,895 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988, 80.788, 80.448, 80.77]
2025-07-14 21:40:43,895 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95, 83.34, 82.56, 81.9]
2025-07-14 21:40:43,895 [trainer.py] => Average Accuracy (CNN): 85.73888888888888 

2025-07-14 21:40:43,896 [trainer.py] => All params: 88788289
2025-07-14 21:40:43,897 [trainer.py] => Trainable params: 2989633
2025-07-14 21:40:43,921 [ranpac.py] => Learning on 180-190
2025-07-14 21:41:51,581 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:41:51,583 [ranpac.py] => [Dynamic-K] Computed K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-14 21:41:51,584 [ranpac.py] => [KNN] task 9, dynamic K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-14 21:41:51,587 [ranpac.py] => [KNN] task 9, weight sparsity: 0.887, distance_metric: mahalanobis
2025-07-14 21:42:25,941 [trainer.py] => No NME accuracy.
2025-07-14 21:42:25,941 [trainer.py] => CNN: {'total': 81.28, '00-99': 86.17, '100-109': 88.18, '110-119': 73.24, '120-129': 66.55, '130-139': 80.69, '140-149': 61.87, '150-159': 78.42, '160-169': 77.85, '170-179': 78.86, '180-189': 76.31, 'old': 81.56, 'new': 76.31}
2025-07-14 21:42:25,941 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988, 80.788, 80.448, 80.77, 78.848]
2025-07-14 21:42:25,941 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95, 83.34, 82.56, 81.9, 81.28]
2025-07-14 21:42:25,941 [trainer.py] => Average Accuracy (CNN): 85.29299999999999 

2025-07-14 21:42:25,942 [trainer.py] => All params: 88888289
2025-07-14 21:42:25,943 [trainer.py] => Trainable params: 3089633
2025-07-14 21:42:25,965 [ranpac.py] => Learning on 190-200
2025-07-14 21:43:12,805 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-14 21:43:12,807 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-14 21:43:12,808 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-14 21:43:12,808 [ranpac.py] => [KNN] task 10, weight sparsity: 0.918, distance_metric: mahalanobis
2025-07-14 21:43:47,711 [trainer.py] => No NME accuracy.
2025-07-14 21:43:47,711 [trainer.py] => CNN: {'total': 80.01, '00-99': 85.1, '100-109': 87.84, '110-119': 73.94, '120-129': 65.86, '130-139': 80.34, '140-149': 61.87, '150-159': 79.11, '160-169': 75.84, '170-179': 77.85, '180-189': 75.26, '190-199': 70.95, 'old': 80.5, 'new': 70.95}
2025-07-14 21:43:47,712 [trainer.py] => CNN HM: [0.0, 88.863, 79.327, 76.762, 84.356, 75.988, 80.788, 80.448, 80.77, 78.848, 75.424]
2025-07-14 21:43:47,712 [trainer.py] => CNN top1 curve: [90.43, 89.72, 88.14, 86.15, 85.46, 83.95, 83.34, 82.56, 81.9, 81.28, 80.01]
2025-07-14 21:43:47,712 [trainer.py] => Average Accuracy (CNN): 84.81272727272727 

2025-07-14 21:43:47,714 [trainer.py] => Forgetting (CNN): 2.446
2025-07-15 15:40:12,911 [trainer.py] => config: ./exps/ranpac.json
2025-07-15 15:40:12,914 [trainer.py] => prefix: reproduce
2025-07-15 15:40:12,914 [trainer.py] => dataset: cub
2025-07-15 15:40:12,914 [trainer.py] => memory_size: 0
2025-07-15 15:40:12,915 [trainer.py] => shuffle: True
2025-07-15 15:40:12,915 [trainer.py] => init_cls: 100
2025-07-15 15:40:12,915 [trainer.py] => increment: 10
2025-07-15 15:40:12,915 [trainer.py] => model_name: ranpac
2025-07-15 15:40:12,915 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 15:40:12,915 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 15:40:12,915 [trainer.py] => seed: 1993
2025-07-15 15:40:12,915 [trainer.py] => resume: False
2025-07-15 15:40:12,915 [trainer.py] => shot: 5
2025-07-15 15:40:12,915 [trainer.py] => use_simplecil: False
2025-07-15 15:40:12,915 [trainer.py] => tuned_epoch: 1
2025-07-15 15:40:12,915 [trainer.py] => init_lr: 0.01
2025-07-15 15:40:12,916 [trainer.py] => batch_size: 48
2025-07-15 15:40:12,916 [trainer.py] => weight_decay: 0.0005
2025-07-15 15:40:12,916 [trainer.py] => min_lr: 0
2025-07-15 15:40:12,916 [trainer.py] => ffn_num: 64
2025-07-15 15:40:12,916 [trainer.py] => optimizer: sgd
2025-07-15 15:40:12,916 [trainer.py] => use_RP: True
2025-07-15 15:40:12,916 [trainer.py] => M: 10000
2025-07-15 15:40:12,916 [trainer.py] => fecam: False
2025-07-15 15:40:12,916 [trainer.py] => calibration: True
2025-07-15 15:40:12,916 [trainer.py] => knn_k: 5
2025-07-15 15:40:12,916 [trainer.py] => knn_distance_metric: cosine
2025-07-15 15:40:12,916 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 15:40:12,917 [trainer.py] => knn_adaptive_k: True
2025-07-15 15:40:12,917 [trainer.py] => knn_temperature: 16.0
2025-07-15 15:40:12,917 [trainer.py] => k_min: 3
2025-07-15 15:40:12,917 [trainer.py] => k_max: 21
2025-07-15 15:40:12,917 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 15:40:12,917 [trainer.py] => cosine_temperature: 16.0
2025-07-15 15:40:12,917 [trainer.py] => use_wasserstein: True
2025-07-15 15:40:12,917 [trainer.py] => wasserstein_alpha: 0.5
2025-07-15 15:40:12,917 [trainer.py] => wasserstein_max_iter: 20
2025-07-15 15:40:12,917 [trainer.py] => wasserstein_tol: 1e-05
2025-07-15 15:40:12,917 [trainer.py] => wasserstein_reg_lambda: 1e-06
2025-07-15 15:40:12,918 [trainer.py] => wasserstein_verbose: True
2025-07-15 15:40:13,080 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 15:40:39,848 [ranpac.py] => [Wasserstein] Initialized with alpha=0.5, max_iter=20, tol=1e-05
2025-07-15 15:40:39,849 [trainer.py] => All params: 86988288
2025-07-15 15:40:39,850 [trainer.py] => Trainable params: 1189632
2025-07-15 15:40:41,470 [ranpac.py] => Learning on 0-100
2025-07-15 15:41:28,003 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 15:41:55,342 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-15 15:41:56,010 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-15 15:41:56,010 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-15 15:41:56,010 [trainer.py] => No NME accuracy.
2025-07-15 15:41:56,010 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-15 15:41:56,010 [trainer.py] => CNN HM: [0.0]
2025-07-15 15:41:56,010 [trainer.py] => CNN top1 curve: [90.43]
2025-07-15 15:41:56,011 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-15 15:41:56,011 [trainer.py] => All params: 87988289
2025-07-15 15:41:56,012 [trainer.py] => Trainable params: 1189633
2025-07-15 15:41:56,024 [ranpac.py] => Learning on 100-110
2025-07-15 15:41:59,590 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:41:59,592 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 15:41:59,592 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 15:41:59,594 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-15 15:42:07,562 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.966s (20 iterations)
2025-07-15 15:42:16,755 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.966s (20 iterations)
2025-07-15 15:42:21,884 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.944s (20 iterations)
2025-07-15 15:42:30,297 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.231s (20 iterations)
2025-07-15 15:42:34,978 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.498s (20 iterations)
2025-07-15 15:42:38,665 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.503s (20 iterations)
2025-07-15 15:42:46,559 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.713s (20 iterations)
2025-07-15 15:42:54,005 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.259s (20 iterations)
2025-07-15 15:42:59,971 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.783s (20 iterations)
2025-07-15 15:43:04,232 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.078s (20 iterations)
2025-07-15 15:43:17,035 [trainer.py] => No NME accuracy.
2025-07-15 15:43:17,035 [trainer.py] => CNN: {'total': 82.36, '00-99': 89.74, '100-109': 10.47, 'old': 89.74, 'new': 10.47}
2025-07-15 15:43:17,035 [trainer.py] => CNN HM: [0.0, 18.752]
2025-07-15 15:43:17,035 [trainer.py] => CNN top1 curve: [90.43, 82.36]
2025-07-15 15:43:17,035 [trainer.py] => Average Accuracy (CNN): 86.39500000000001 

2025-07-15 15:43:17,036 [trainer.py] => All params: 88088289
2025-07-15 15:43:17,037 [trainer.py] => Trainable params: 2289633
2025-07-15 15:43:17,053 [ranpac.py] => Learning on 110-120
2025-07-15 15:43:20,283 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:43:20,286 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 15:43:20,286 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 15:43:20,287 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-15 15:43:22,651 [wasserstein_calibration.py] => Wasserstein barycenter computed in 2.362s (20 iterations)
2025-07-15 15:43:29,685 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.826s (20 iterations)
2025-07-15 15:43:35,308 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.439s (20 iterations)
2025-07-15 15:43:40,844 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.356s (20 iterations)
2025-07-15 15:43:49,765 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.738s (20 iterations)
2025-07-15 15:43:59,173 [wasserstein_calibration.py] => Wasserstein barycenter computed in 9.221s (20 iterations)
2025-07-15 15:44:05,804 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.447s (20 iterations)
2025-07-15 15:44:09,088 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.102s (20 iterations)
2025-07-15 15:44:16,717 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.445s (20 iterations)
2025-07-15 15:44:25,577 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.676s (20 iterations)
2025-07-15 15:44:39,160 [trainer.py] => No NME accuracy.
2025-07-15 15:44:39,161 [trainer.py] => CNN: {'total': 75.01, '00-99': 88.73, '100-109': 8.78, '110-119': 4.58, 'old': 81.3, 'new': 4.58}
2025-07-15 15:44:39,161 [trainer.py] => CNN HM: [0.0, 18.752, 8.671]
2025-07-15 15:44:39,162 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01]
2025-07-15 15:44:39,162 [trainer.py] => Average Accuracy (CNN): 82.60000000000001 

2025-07-15 15:44:39,164 [trainer.py] => All params: 88188289
2025-07-15 15:44:39,165 [trainer.py] => Trainable params: 2389633
2025-07-15 15:44:39,183 [ranpac.py] => Learning on 120-130
2025-07-15 15:44:44,322 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:44:44,324 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 15:44:44,325 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 15:44:44,326 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-15 15:44:49,684 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.356s (20 iterations)
2025-07-15 15:44:55,758 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.891s (20 iterations)
2025-07-15 15:45:01,265 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.325s (20 iterations)
2025-07-15 15:45:10,305 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.855s (20 iterations)
2025-07-15 15:45:14,524 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.033s (20 iterations)
2025-07-15 15:45:17,824 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.118s (20 iterations)
2025-07-15 15:45:22,039 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.035s (20 iterations)
2025-07-15 15:45:27,249 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.027s (20 iterations)
2025-07-15 15:45:31,929 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.496s (20 iterations)
2025-07-15 15:45:37,042 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.932s (20 iterations)
2025-07-15 15:45:51,637 [trainer.py] => No NME accuracy.
2025-07-15 15:45:51,638 [trainer.py] => CNN: {'total': 69.93, '00-99': 88.87, '100-109': 7.43, '110-119': 9.15, '120-129': 4.83, 'old': 75.38, 'new': 4.83}
2025-07-15 15:45:51,638 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078]
2025-07-15 15:45:51,638 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93]
2025-07-15 15:45:51,638 [trainer.py] => Average Accuracy (CNN): 79.4325 

2025-07-15 15:45:51,640 [trainer.py] => All params: 88288289
2025-07-15 15:45:51,642 [trainer.py] => Trainable params: 2489633
2025-07-15 15:45:51,658 [ranpac.py] => Learning on 130-140
2025-07-15 15:45:54,923 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:45:54,925 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-15 15:45:54,925 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-15 15:45:54,926 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-15 15:46:00,286 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.358s (20 iterations)
2025-07-15 15:46:10,813 [wasserstein_calibration.py] => Wasserstein barycenter computed in 10.336s (20 iterations)
2025-07-15 15:46:17,924 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.869s (20 iterations)
2025-07-15 15:46:24,332 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.224s (20 iterations)
2025-07-15 15:46:32,153 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.638s (20 iterations)
2025-07-15 15:46:35,956 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.619s (20 iterations)
2025-07-15 15:46:41,577 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.441s (20 iterations)
2025-07-15 15:46:46,654 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.894s (20 iterations)
2025-07-15 15:46:52,779 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.942s (20 iterations)
2025-07-15 15:46:58,758 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.795s (20 iterations)
2025-07-15 15:47:14,689 [trainer.py] => No NME accuracy.
2025-07-15 15:47:14,689 [trainer.py] => CNN: {'total': 64.97, '00-99': 88.98, '100-109': 7.09, '110-119': 9.51, '120-129': 4.48, '130-139': 0.0, 'old': 69.99, 'new': 0.0}
2025-07-15 15:47:14,689 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0]
2025-07-15 15:47:14,690 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97]
2025-07-15 15:47:14,690 [trainer.py] => Average Accuracy (CNN): 76.54 

2025-07-15 15:47:14,691 [trainer.py] => All params: 88388289
2025-07-15 15:47:14,693 [trainer.py] => Trainable params: 2589633
2025-07-15 15:47:14,711 [ranpac.py] => Learning on 140-150
2025-07-15 15:47:17,732 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:47:17,734 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-15 15:47:17,734 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-15 15:47:17,735 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: cosine
2025-07-15 15:47:23,897 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.159s (20 iterations)
2025-07-15 15:47:32,250 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.157s (20 iterations)
2025-07-15 15:47:38,713 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.275s (20 iterations)
2025-07-15 15:47:42,825 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.930s (20 iterations)
2025-07-15 15:47:47,362 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.353s (20 iterations)
2025-07-15 15:47:54,055 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.508s (20 iterations)
2025-07-15 15:48:00,518 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.277s (20 iterations)
2025-07-15 15:48:03,773 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.073s (20 iterations)
2025-07-15 15:48:08,376 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.420s (20 iterations)
2025-07-15 15:48:13,461 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.900s (20 iterations)
2025-07-15 15:48:32,598 [trainer.py] => No NME accuracy.
2025-07-15 15:48:32,599 [trainer.py] => CNN: {'total': 60.63, '00-99': 88.94, '100-109': 6.42, '110-119': 9.15, '120-129': 3.45, '130-139': 0.0, '140-149': 0.0, 'old': 64.8, 'new': 0.0}
2025-07-15 15:48:32,599 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0]
2025-07-15 15:48:32,599 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63]
2025-07-15 15:48:32,599 [trainer.py] => Average Accuracy (CNN): 73.88833333333334 

2025-07-15 15:48:32,601 [trainer.py] => All params: 88488289
2025-07-15 15:48:32,603 [trainer.py] => Trainable params: 2689633
2025-07-15 15:48:32,624 [ranpac.py] => Learning on 150-160
2025-07-15 15:48:36,520 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:48:36,522 [ranpac.py] => [Dynamic-K] Computed K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-15 15:48:36,522 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-15 15:48:36,523 [ranpac.py] => [KNN] task 6, weight sparsity: 0.910, distance_metric: cosine
2025-07-15 15:48:41,314 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.789s (20 iterations)
2025-07-15 15:48:47,840 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.337s (20 iterations)
2025-07-15 15:48:52,415 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.395s (20 iterations)
2025-07-15 15:48:56,503 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.906s (20 iterations)
2025-07-15 15:49:01,889 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.205s (20 iterations)
2025-07-15 15:49:06,992 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.918s (20 iterations)
2025-07-15 15:49:12,031 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.847s (20 iterations)
2025-07-15 15:49:16,444 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.230s (20 iterations)
2025-07-15 15:49:25,495 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.867s (20 iterations)
2025-07-15 15:49:30,886 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.210s (20 iterations)
2025-07-15 15:49:49,318 [trainer.py] => No NME accuracy.
2025-07-15 15:49:49,319 [trainer.py] => CNN: {'total': 56.73, '00-99': 88.87, '100-109': 5.74, '110-119': 8.8, '120-129': 2.76, '130-139': 0.0, '140-149': 0.0, '150-159': 1.37, 'old': 60.47, 'new': 1.37}
2025-07-15 15:49:49,319 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0, 2.679]
2025-07-15 15:49:49,319 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63, 56.73]
2025-07-15 15:49:49,320 [trainer.py] => Average Accuracy (CNN): 71.43714285714286 

2025-07-15 15:49:49,321 [trainer.py] => All params: 88588289
2025-07-15 15:49:49,322 [trainer.py] => Trainable params: 2789633
2025-07-15 15:49:49,343 [ranpac.py] => Learning on 160-170
2025-07-15 15:49:52,485 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:49:52,489 [ranpac.py] => [Dynamic-K] Computed K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-15 15:49:52,490 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-15 15:49:52,491 [ranpac.py] => [KNN] task 7, weight sparsity: 0.904, distance_metric: cosine
2025-07-15 15:50:00,684 [wasserstein_calibration.py] => Wasserstein barycenter computed in 8.190s (20 iterations)
2025-07-15 15:50:07,875 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.004s (20 iterations)
2025-07-15 15:50:14,980 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.920s (20 iterations)
2025-07-15 15:50:18,678 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.520s (20 iterations)
2025-07-15 15:50:26,254 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.397s (20 iterations)
2025-07-15 15:50:30,989 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.557s (20 iterations)
2025-07-15 15:50:37,180 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.009s (20 iterations)
2025-07-15 15:50:40,946 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.582s (20 iterations)
2025-07-15 15:50:47,577 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.446s (20 iterations)
2025-07-15 15:50:51,984 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.203s (20 iterations)
2025-07-15 15:51:09,890 [trainer.py] => No NME accuracy.
2025-07-15 15:51:09,890 [trainer.py] => CNN: {'total': 54.39, '00-99': 88.8, '100-109': 5.74, '110-119': 8.8, '120-129': 2.07, '130-139': 0.0, '140-149': 0.0, '150-159': 1.03, '160-169': 19.8, 'old': 56.62, 'new': 19.8}
2025-07-15 15:51:09,890 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0, 2.679, 29.34]
2025-07-15 15:51:09,891 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63, 56.73, 54.39]
2025-07-15 15:51:09,891 [trainer.py] => Average Accuracy (CNN): 69.30625 

2025-07-15 15:51:09,892 [trainer.py] => All params: 88688289
2025-07-15 15:51:09,893 [trainer.py] => Trainable params: 2889633
2025-07-15 15:51:09,914 [ranpac.py] => Learning on 170-180
2025-07-15 15:51:13,053 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:51:13,056 [ranpac.py] => [Dynamic-K] Computed K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-15 15:51:13,057 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-15 15:51:13,058 [ranpac.py] => [KNN] task 8, weight sparsity: 0.915, distance_metric: cosine
2025-07-15 15:51:20,406 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.345s (20 iterations)
2025-07-15 15:51:25,065 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.469s (20 iterations)
2025-07-15 15:51:31,591 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.343s (20 iterations)
2025-07-15 15:51:36,234 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.463s (20 iterations)
2025-07-15 15:51:41,403 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.987s (20 iterations)
2025-07-15 15:51:46,106 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.521s (20 iterations)
2025-07-15 15:51:48,926 [wasserstein_calibration.py] => Wasserstein barycenter computed in 2.638s (20 iterations)
2025-07-15 15:51:55,111 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.001s (20 iterations)
2025-07-15 15:52:03,220 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.923s (20 iterations)
2025-07-15 15:52:07,087 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.684s (20 iterations)
2025-07-15 15:52:26,960 [trainer.py] => No NME accuracy.
2025-07-15 15:52:26,961 [trainer.py] => CNN: {'total': 51.37, '00-99': 88.77, '100-109': 5.41, '110-119': 8.8, '120-129': 1.72, '130-139': 0.0, '140-149': 0.0, '150-159': 1.03, '160-169': 19.8, '170-179': 2.68, 'old': 54.33, 'new': 2.68}
2025-07-15 15:52:26,961 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0, 2.679, 29.34, 5.108]
2025-07-15 15:52:26,961 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63, 56.73, 54.39, 51.37]
2025-07-15 15:52:26,961 [trainer.py] => Average Accuracy (CNN): 67.31333333333333 

2025-07-15 15:52:26,963 [trainer.py] => All params: 88788289
2025-07-15 15:52:26,964 [trainer.py] => Trainable params: 2989633
2025-07-15 15:52:26,984 [ranpac.py] => Learning on 180-190
2025-07-15 15:52:30,504 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:52:30,505 [ranpac.py] => [Dynamic-K] Computed K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-15 15:52:30,505 [ranpac.py] => [KNN] task 9, dynamic K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-15 15:52:30,506 [ranpac.py] => [KNN] task 9, weight sparsity: 0.887, distance_metric: cosine
2025-07-15 15:52:37,721 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.211s (20 iterations)
2025-07-15 15:52:45,411 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.492s (20 iterations)
2025-07-15 15:52:50,618 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.024s (20 iterations)
2025-07-15 15:52:56,806 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.004s (20 iterations)
2025-07-15 15:53:01,951 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.966s (20 iterations)
2025-07-15 15:53:07,580 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.447s (20 iterations)
2025-07-15 15:53:16,795 [wasserstein_calibration.py] => Wasserstein barycenter computed in 9.013s (20 iterations)
2025-07-15 15:53:23,831 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.851s (20 iterations)
2025-07-15 15:53:31,650 [wasserstein_calibration.py] => Wasserstein barycenter computed in 7.634s (20 iterations)
2025-07-15 15:53:36,770 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.936s (20 iterations)
2025-07-15 15:54:03,255 [trainer.py] => No NME accuracy.
2025-07-15 15:54:03,255 [trainer.py] => CNN: {'total': 49.16, '00-99': 88.67, '100-109': 5.41, '110-119': 8.8, '120-129': 1.72, '130-139': 0.0, '140-149': 0.0, '150-159': 0.68, '160-169': 19.8, '170-179': 2.68, '180-189': 10.45, 'old': 51.3, 'new': 10.45}
2025-07-15 15:54:03,256 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0, 2.679, 29.34, 5.108, 17.363]
2025-07-15 15:54:03,256 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63, 56.73, 54.39, 51.37, 49.16]
2025-07-15 15:54:03,256 [trainer.py] => Average Accuracy (CNN): 65.498 

2025-07-15 15:54:03,258 [trainer.py] => All params: 88888289
2025-07-15 15:54:03,258 [trainer.py] => Trainable params: 3089633
2025-07-15 15:54:03,279 [ranpac.py] => Learning on 190-200
2025-07-15 15:54:06,157 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 15:54:06,159 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-15 15:54:06,160 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-15 15:54:06,161 [ranpac.py] => [KNN] task 10, weight sparsity: 0.918, distance_metric: cosine
2025-07-15 15:54:10,075 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.912s (20 iterations)
2025-07-15 15:54:16,474 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.207s (20 iterations)
2025-07-15 15:54:22,730 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.072s (20 iterations)
2025-07-15 15:54:27,536 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.624s (20 iterations)
2025-07-15 15:54:33,179 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.456s (20 iterations)
2025-07-15 15:54:38,890 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.531s (20 iterations)
2025-07-15 15:54:44,195 [wasserstein_calibration.py] => Wasserstein barycenter computed in 5.125s (20 iterations)
2025-07-15 15:54:47,960 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.561s (20 iterations)
2025-07-15 15:54:54,605 [wasserstein_calibration.py] => Wasserstein barycenter computed in 6.462s (20 iterations)
2025-07-15 15:54:59,239 [wasserstein_calibration.py] => Wasserstein barycenter computed in 4.450s (20 iterations)
2025-07-15 15:55:23,368 [trainer.py] => No NME accuracy.
2025-07-15 15:55:23,368 [trainer.py] => CNN: {'total': 47.12, '00-99': 88.7, '100-109': 5.41, '110-119': 8.8, '120-129': 1.72, '130-139': 0.0, '140-149': 0.0, '150-159': 0.68, '160-169': 9.73, '170-179': 2.68, '180-189': 10.45, '190-199': 18.92, 'old': 48.64, 'new': 18.92}
2025-07-15 15:55:23,370 [trainer.py] => CNN HM: [0.0, 18.752, 8.671, 9.078, 0.0, 0.0, 2.679, 29.34, 5.108, 17.363, 27.243]
2025-07-15 15:55:23,370 [trainer.py] => CNN top1 curve: [90.43, 82.36, 75.01, 69.93, 64.97, 60.63, 56.73, 54.39, 51.37, 49.16, 47.12]
2025-07-15 15:55:23,370 [trainer.py] => Average Accuracy (CNN): 63.82727272727273 

2025-07-15 15:55:23,371 [trainer.py] => Forgetting (CNN): 2.1370000000000005
2025-07-15 15:58:24,703 [trainer.py] => config: ./exps/ranpac.json
2025-07-15 15:58:24,703 [trainer.py] => prefix: reproduce
2025-07-15 15:58:24,704 [trainer.py] => dataset: cub
2025-07-15 15:58:24,704 [trainer.py] => memory_size: 0
2025-07-15 15:58:24,704 [trainer.py] => shuffle: True
2025-07-15 15:58:24,704 [trainer.py] => init_cls: 100
2025-07-15 15:58:24,704 [trainer.py] => increment: 10
2025-07-15 15:58:24,704 [trainer.py] => model_name: ranpac
2025-07-15 15:58:24,704 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 15:58:24,704 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 15:58:24,705 [trainer.py] => seed: 1993
2025-07-15 15:58:24,705 [trainer.py] => resume: False
2025-07-15 15:58:24,705 [trainer.py] => shot: 5
2025-07-15 15:58:24,705 [trainer.py] => use_simplecil: False
2025-07-15 15:58:24,705 [trainer.py] => tuned_epoch: 1
2025-07-15 15:58:24,705 [trainer.py] => init_lr: 0.01
2025-07-15 15:58:24,705 [trainer.py] => batch_size: 48
2025-07-15 15:58:24,705 [trainer.py] => weight_decay: 0.0005
2025-07-15 15:58:24,705 [trainer.py] => min_lr: 0
2025-07-15 15:58:24,705 [trainer.py] => ffn_num: 64
2025-07-15 15:58:24,706 [trainer.py] => optimizer: sgd
2025-07-15 15:58:24,706 [trainer.py] => use_RP: True
2025-07-15 15:58:24,706 [trainer.py] => M: 10000
2025-07-15 15:58:24,706 [trainer.py] => fecam: False
2025-07-15 15:58:24,706 [trainer.py] => calibration: True
2025-07-15 15:58:24,706 [trainer.py] => knn_k: 5
2025-07-15 15:58:24,706 [trainer.py] => knn_distance_metric: cosine
2025-07-15 15:58:24,706 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 15:58:24,706 [trainer.py] => knn_adaptive_k: True
2025-07-15 15:58:24,706 [trainer.py] => knn_temperature: 16.0
2025-07-15 15:58:24,706 [trainer.py] => k_min: 3
2025-07-15 15:58:24,707 [trainer.py] => k_max: 21
2025-07-15 15:58:24,707 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 15:58:24,707 [trainer.py] => cosine_temperature: 16.0
2025-07-15 15:58:24,707 [trainer.py] => use_wasserstein: True
2025-07-15 15:58:24,707 [trainer.py] => wasserstein_alpha: 0.1
2025-07-15 15:58:24,707 [trainer.py] => wasserstein_max_iter: 3
2025-07-15 15:58:24,707 [trainer.py] => wasserstein_tol: 1e-05
2025-07-15 15:58:24,707 [trainer.py] => wasserstein_reg_lambda: 1e-06
2025-07-15 15:58:24,707 [trainer.py] => wasserstein_verbose: True
2025-07-15 15:58:24,817 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 15:58:47,090 [ranpac.py] => [Wasserstein] Initialized with alpha=0.1, max_iter=3, tol=1e-05
2025-07-15 15:58:47,091 [trainer.py] => All params: 86988288
2025-07-15 15:58:47,091 [trainer.py] => Trainable params: 1189632
2025-07-15 15:58:48,691 [ranpac.py] => Learning on 0-100
2025-07-15 15:59:27,953 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 15:59:56,734 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-15 15:59:57,398 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-15 15:59:57,398 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-15 15:59:57,398 [trainer.py] => No NME accuracy.
2025-07-15 15:59:57,398 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-15 15:59:57,399 [trainer.py] => CNN HM: [0.0]
2025-07-15 15:59:57,399 [trainer.py] => CNN top1 curve: [90.43]
2025-07-15 15:59:57,399 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-15 15:59:57,400 [trainer.py] => All params: 87988289
2025-07-15 15:59:57,401 [trainer.py] => Trainable params: 1189633
2025-07-15 15:59:57,413 [ranpac.py] => Learning on 100-110
2025-07-15 16:00:02,963 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:00:02,966 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 16:00:02,966 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 16:00:02,967 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-15 16:00:04,037 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.066s (3 iterations)
2025-07-15 16:00:04,576 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.097484), adding regularization
2025-07-15 16:00:06,815 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.413s (3 iterations)
2025-07-15 16:00:07,030 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.126694), adding regularization
2025-07-15 16:00:08,344 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.911s (3 iterations)
2025-07-15 16:00:08,524 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.132539), adding regularization
2025-07-15 16:00:10,239 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.332s (3 iterations)
2025-07-15 16:00:10,429 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.122338), adding regularization
2025-07-15 16:00:11,546 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.762s (3 iterations)
2025-07-15 16:00:11,734 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.104108), adding regularization
2025-07-15 16:00:12,729 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.611s (3 iterations)
2025-07-15 16:00:12,926 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.709532), adding regularization
2025-07-15 16:00:14,555 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.252s (3 iterations)
2025-07-15 16:00:14,761 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.117809), adding regularization
2025-07-15 16:00:16,378 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.222s (3 iterations)
2025-07-15 16:00:16,555 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.111772), adding regularization
2025-07-15 16:00:17,888 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.961s (3 iterations)
2025-07-15 16:00:18,085 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.118145), adding regularization
2025-07-15 16:00:19,152 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.688s (3 iterations)
2025-07-15 16:00:19,329 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.315342), adding regularization
2025-07-15 16:00:32,185 [trainer.py] => No NME accuracy.
2025-07-15 16:00:32,185 [trainer.py] => CNN: {'total': 81.42, '00-99': 89.77, '100-109': 0.0, 'old': 89.77, 'new': 0.0}
2025-07-15 16:00:32,186 [trainer.py] => CNN HM: [0.0, 0.0]
2025-07-15 16:00:32,186 [trainer.py] => CNN top1 curve: [90.43, 81.42]
2025-07-15 16:00:32,186 [trainer.py] => Average Accuracy (CNN): 85.92500000000001 

2025-07-15 16:00:32,188 [trainer.py] => All params: 88088289
2025-07-15 16:00:32,189 [trainer.py] => Trainable params: 2289633
2025-07-15 16:00:32,204 [ranpac.py] => Learning on 110-120
2025-07-15 16:00:35,246 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:00:35,248 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 16:00:35,248 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 16:00:35,249 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-15 16:00:35,629 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.379s (3 iterations)
2025-07-15 16:00:35,827 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.182437), adding regularization
2025-07-15 16:00:37,352 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.121s (3 iterations)
2025-07-15 16:00:37,556 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.209124), adding regularization
2025-07-15 16:00:38,949 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.933s (3 iterations)
2025-07-15 16:00:39,158 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.345788), adding regularization
2025-07-15 16:00:40,500 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.955s (3 iterations)
2025-07-15 16:00:40,683 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.103865), adding regularization
2025-07-15 16:00:42,417 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.368s (3 iterations)
2025-07-15 16:00:43,009 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.149236), adding regularization
2025-07-15 16:00:47,850 [wasserstein_calibration.py] => Wasserstein barycenter computed in 3.892s (3 iterations)
2025-07-15 16:00:48,070 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.133246), adding regularization
2025-07-15 16:00:49,510 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.042s (3 iterations)
2025-07-15 16:00:49,686 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.184161), adding regularization
2025-07-15 16:00:50,666 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.583s (3 iterations)
2025-07-15 16:00:50,862 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.236838), adding regularization
2025-07-15 16:00:52,525 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.275s (3 iterations)
2025-07-15 16:00:52,708 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.038789), adding regularization
2025-07-15 16:00:54,705 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.607s (3 iterations)
2025-07-15 16:00:54,903 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.361524), adding regularization
2025-07-15 16:01:07,429 [trainer.py] => No NME accuracy.
2025-07-15 16:01:07,430 [trainer.py] => CNN: {'total': 73.59, '00-99': 88.39, '100-109': 0.0, '110-119': 0.0, 'old': 80.16, 'new': 0.0}
2025-07-15 16:01:07,430 [trainer.py] => CNN HM: [0.0, 0.0, 0.0]
2025-07-15 16:01:07,430 [trainer.py] => CNN top1 curve: [90.43, 81.42, 73.59]
2025-07-15 16:01:07,430 [trainer.py] => Average Accuracy (CNN): 81.81333333333335 

2025-07-15 16:01:07,431 [trainer.py] => All params: 88188289
2025-07-15 16:01:07,432 [trainer.py] => Trainable params: 2389633
2025-07-15 16:01:07,446 [ranpac.py] => Learning on 120-130
2025-07-15 16:01:11,932 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:01:11,934 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 16:01:11,934 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 16:01:11,935 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-15 16:01:12,726 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.789s (3 iterations)
2025-07-15 16:01:12,915 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.103570), adding regularization
2025-07-15 16:01:14,306 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.028s (3 iterations)
2025-07-15 16:01:14,508 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.145860), adding regularization
2025-07-15 16:01:15,803 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.918s (3 iterations)
2025-07-15 16:01:16,008 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.205539), adding regularization
2025-07-15 16:01:17,821 [wasserstein_calibration.py] => Wasserstein barycenter computed in 1.414s (3 iterations)
2025-07-15 16:01:18,031 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.332176), adding regularization
2025-07-15 16:01:19,111 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.697s (3 iterations)
2025-07-15 16:01:19,321 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.136539), adding regularization
2025-07-15 16:01:20,258 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.564s (3 iterations)
2025-07-15 16:01:20,480 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.285044), adding regularization
2025-07-15 16:01:21,612 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.741s (3 iterations)
2025-07-15 16:01:21,817 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.206760), adding regularization
2025-07-15 16:01:23,075 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.855s (3 iterations)
2025-07-15 16:01:23,276 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.178800), adding regularization
2025-07-15 16:01:24,430 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.747s (3 iterations)
2025-07-15 16:01:24,611 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.096764), adding regularization
2025-07-15 16:01:25,820 [wasserstein_calibration.py] => Wasserstein barycenter computed in 0.843s (3 iterations)
2025-07-15 16:01:26,021 [wasserstein_calibration.py] => Result not positive definite (min eigenval: -0.168090), adding regularization
2025-07-15 16:02:05,596 [trainer.py] => config: ./exps/ranpac.json
2025-07-15 16:02:05,596 [trainer.py] => prefix: reproduce
2025-07-15 16:02:05,596 [trainer.py] => dataset: cub
2025-07-15 16:02:05,596 [trainer.py] => memory_size: 0
2025-07-15 16:02:05,596 [trainer.py] => shuffle: True
2025-07-15 16:02:05,597 [trainer.py] => init_cls: 100
2025-07-15 16:02:05,597 [trainer.py] => increment: 10
2025-07-15 16:02:05,597 [trainer.py] => model_name: ranpac
2025-07-15 16:02:05,597 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 16:02:05,597 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 16:02:05,597 [trainer.py] => seed: 1993
2025-07-15 16:02:05,597 [trainer.py] => resume: False
2025-07-15 16:02:05,597 [trainer.py] => shot: 5
2025-07-15 16:02:05,597 [trainer.py] => use_simplecil: False
2025-07-15 16:02:05,597 [trainer.py] => tuned_epoch: 1
2025-07-15 16:02:05,598 [trainer.py] => init_lr: 0.01
2025-07-15 16:02:05,598 [trainer.py] => batch_size: 48
2025-07-15 16:02:05,598 [trainer.py] => weight_decay: 0.0005
2025-07-15 16:02:05,598 [trainer.py] => min_lr: 0
2025-07-15 16:02:05,598 [trainer.py] => ffn_num: 64
2025-07-15 16:02:05,598 [trainer.py] => optimizer: sgd
2025-07-15 16:02:05,598 [trainer.py] => use_RP: True
2025-07-15 16:02:05,598 [trainer.py] => M: 10000
2025-07-15 16:02:05,598 [trainer.py] => fecam: False
2025-07-15 16:02:05,598 [trainer.py] => calibration: True
2025-07-15 16:02:05,599 [trainer.py] => knn_k: 5
2025-07-15 16:02:05,599 [trainer.py] => knn_distance_metric: cosine
2025-07-15 16:02:05,599 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 16:02:05,599 [trainer.py] => knn_adaptive_k: True
2025-07-15 16:02:05,599 [trainer.py] => knn_temperature: 16.0
2025-07-15 16:02:05,599 [trainer.py] => k_min: 3
2025-07-15 16:02:05,599 [trainer.py] => k_max: 21
2025-07-15 16:02:05,599 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 16:02:05,599 [trainer.py] => cosine_temperature: 16.0
2025-07-15 16:02:05,732 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 16:02:22,491 [trainer.py] => All params: 86988288
2025-07-15 16:02:22,492 [trainer.py] => Trainable params: 1189632
2025-07-15 16:02:24,091 [ranpac.py] => Learning on 0-100
2025-07-15 16:02:55,311 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 16:03:21,391 [ranpac.py] => [Dynamic-K] Computing base similarity stats for 100 base classes
2025-07-15 16:03:21,936 [ranpac.py] => [Dynamic-K] Base similarity range: [6.6863, 10.5749]
2025-07-15 16:03:21,936 [ranpac.py] => [Dynamic-K] Base similarity stats computed after base task
2025-07-15 16:03:21,936 [trainer.py] => No NME accuracy.
2025-07-15 16:03:21,936 [trainer.py] => CNN: {'total': 90.43, '00-99': 90.43, 'old': 0, 'new': 90.43}
2025-07-15 16:03:21,936 [trainer.py] => CNN HM: [0.0]
2025-07-15 16:03:21,936 [trainer.py] => CNN top1 curve: [90.43]
2025-07-15 16:03:21,937 [trainer.py] => Average Accuracy (CNN): 90.43 

2025-07-15 16:03:21,937 [trainer.py] => All params: 87988289
2025-07-15 16:03:21,938 [trainer.py] => Trainable params: 1189633
2025-07-15 16:03:21,948 [ranpac.py] => Learning on 100-110
2025-07-15 16:03:24,314 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:03:24,318 [ranpac.py] => [Dynamic-K] Computed K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 16:03:24,318 [ranpac.py] => [KNN] task 1, dynamic K values: [14, 17, 8, 15, 7, 5, 14, 13, 10, 6]
2025-07-15 16:03:24,320 [ranpac.py] => [KNN] task 1, weight sparsity: 0.891, distance_metric: cosine
2025-07-15 16:03:41,645 [trainer.py] => No NME accuracy.
2025-07-15 16:03:41,645 [trainer.py] => CNN: {'total': 89.63, '00-99': 89.84, '100-109': 87.5, 'old': 89.84, 'new': 87.5}
2025-07-15 16:03:41,646 [trainer.py] => CNN HM: [0.0, 88.655]
2025-07-15 16:03:41,646 [trainer.py] => CNN top1 curve: [90.43, 89.63]
2025-07-15 16:03:41,646 [trainer.py] => Average Accuracy (CNN): 90.03 

2025-07-15 16:03:41,647 [trainer.py] => All params: 88088289
2025-07-15 16:03:41,649 [trainer.py] => Trainable params: 2289633
2025-07-15 16:03:41,664 [ranpac.py] => Learning on 110-120
2025-07-15 16:03:43,169 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:03:43,171 [ranpac.py] => [Dynamic-K] Computed K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 16:03:43,171 [ranpac.py] => [KNN] task 2, dynamic K values: [3, 12, 9, 9, 16, 17, 11, 4, 14, 16]
2025-07-15 16:03:43,172 [ranpac.py] => [KNN] task 2, weight sparsity: 0.889, distance_metric: cosine
2025-07-15 16:03:57,412 [trainer.py] => No NME accuracy.
2025-07-15 16:03:57,413 [trainer.py] => CNN: {'total': 88.11, '00-99': 89.64, '100-109': 88.18, '110-119': 72.54, 'old': 89.5, 'new': 72.54}
2025-07-15 16:03:57,413 [trainer.py] => CNN HM: [0.0, 88.655, 80.132]
2025-07-15 16:03:57,413 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11]
2025-07-15 16:03:57,413 [trainer.py] => Average Accuracy (CNN): 89.39 

2025-07-15 16:03:57,415 [trainer.py] => All params: 88188289
2025-07-15 16:03:57,416 [trainer.py] => Trainable params: 2389633
2025-07-15 16:03:57,431 [ranpac.py] => Learning on 120-130
2025-07-15 16:03:59,352 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:03:59,356 [ranpac.py] => [Dynamic-K] Computed K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 16:03:59,356 [ranpac.py] => [KNN] task 3, dynamic K values: [9, 10, 9, 16, 6, 4, 6, 8, 7, 8]
2025-07-15 16:03:59,357 [ranpac.py] => [KNN] task 3, weight sparsity: 0.917, distance_metric: cosine
2025-07-15 16:04:16,804 [trainer.py] => No NME accuracy.
2025-07-15 16:04:16,805 [trainer.py] => CNN: {'total': 86.39, '00-99': 88.87, '100-109': 89.53, '110-119': 73.59, '120-129': 71.03, 'old': 87.68, 'new': 71.03}
2025-07-15 16:04:16,805 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482]
2025-07-15 16:04:16,805 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39]
2025-07-15 16:04:16,805 [trainer.py] => Average Accuracy (CNN): 88.64 

2025-07-15 16:04:16,807 [trainer.py] => All params: 88288289
2025-07-15 16:04:16,809 [trainer.py] => Trainable params: 2489633
2025-07-15 16:04:16,823 [ranpac.py] => Learning on 130-140
2025-07-15 16:04:20,635 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:04:20,637 [ranpac.py] => [Dynamic-K] Computed K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-15 16:04:20,637 [ranpac.py] => [KNN] task 4, dynamic K values: [9, 14, 10, 11, 14, 5, 9, 8, 10, 10]
2025-07-15 16:04:20,638 [ranpac.py] => [KNN] task 4, weight sparsity: 0.900, distance_metric: cosine
2025-07-15 16:04:40,900 [trainer.py] => No NME accuracy.
2025-07-15 16:04:40,900 [trainer.py] => CNN: {'total': 85.74, '00-99': 88.77, '100-109': 89.86, '110-119': 73.59, '120-129': 67.59, '130-139': 81.38, 'old': 86.07, 'new': 81.38}
2025-07-15 16:04:40,900 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659]
2025-07-15 16:04:40,900 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74]
2025-07-15 16:04:40,900 [trainer.py] => Average Accuracy (CNN): 88.06 

2025-07-15 16:04:40,901 [trainer.py] => All params: 88388289
2025-07-15 16:04:40,902 [trainer.py] => Trainable params: 2589633
2025-07-15 16:04:40,920 [ranpac.py] => Learning on 140-150
2025-07-15 16:04:43,942 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:04:43,944 [ranpac.py] => [Dynamic-K] Computed K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-15 16:04:43,944 [ranpac.py] => [KNN] task 5, dynamic K values: [11, 15, 11, 6, 7, 12, 11, 4, 7, 8]
2025-07-15 16:04:43,945 [ranpac.py] => [KNN] task 5, weight sparsity: 0.908, distance_metric: cosine
2025-07-15 16:05:05,492 [trainer.py] => No NME accuracy.
2025-07-15 16:05:05,492 [trainer.py] => CNN: {'total': 84.25, '00-99': 87.9, '100-109': 90.54, '110-119': 71.83, '120-129': 69.66, '130-139': 82.07, '140-149': 69.78, 'old': 85.24, 'new': 69.78}
2025-07-15 16:05:05,493 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739]
2025-07-15 16:05:05,493 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25]
2025-07-15 16:05:05,493 [trainer.py] => Average Accuracy (CNN): 87.425 

2025-07-15 16:05:05,495 [trainer.py] => All params: 88488289
2025-07-15 16:05:05,496 [trainer.py] => Trainable params: 2689633
2025-07-15 16:05:05,513 [ranpac.py] => Learning on 150-160
2025-07-15 16:05:09,438 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:05:09,440 [ranpac.py] => [Dynamic-K] Computed K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-15 16:05:09,440 [ranpac.py] => [KNN] task 6, dynamic K values: [8, 11, 7, 6, 9, 8, 8, 7, 17, 9]
2025-07-15 16:05:09,441 [ranpac.py] => [KNN] task 6, weight sparsity: 0.910, distance_metric: cosine
2025-07-15 16:05:33,365 [trainer.py] => No NME accuracy.
2025-07-15 16:05:33,366 [trainer.py] => CNN: {'total': 83.92, '00-99': 87.63, '100-109': 88.85, '110-119': 73.59, '120-129': 68.97, '130-139': 82.76, '140-149': 70.14, '150-159': 81.51, 'old': 84.09, 'new': 81.51}
2025-07-15 16:05:33,366 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78]
2025-07-15 16:05:33,366 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92]
2025-07-15 16:05:33,366 [trainer.py] => Average Accuracy (CNN): 86.9242857142857 

2025-07-15 16:05:33,367 [trainer.py] => All params: 88588289
2025-07-15 16:05:33,368 [trainer.py] => Trainable params: 2789633
2025-07-15 16:05:33,387 [ranpac.py] => Learning on 160-170
2025-07-15 16:05:37,712 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:05:37,714 [ranpac.py] => [Dynamic-K] Computed K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-15 16:05:37,714 [ranpac.py] => [KNN] task 7, dynamic K values: [15, 12, 12, 5, 13, 7, 10, 5, 11, 6]
2025-07-15 16:05:37,715 [ranpac.py] => [KNN] task 7, weight sparsity: 0.904, distance_metric: cosine
2025-07-15 16:06:01,817 [trainer.py] => No NME accuracy.
2025-07-15 16:06:01,817 [trainer.py] => CNN: {'total': 83.11, '00-99': 87.07, '100-109': 88.51, '110-119': 74.3, '120-129': 69.66, '130-139': 81.03, '140-149': 66.19, '150-159': 81.16, '160-169': 80.54, 'old': 83.27, 'new': 80.54}
2025-07-15 16:06:01,817 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882]
2025-07-15 16:06:01,818 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11]
2025-07-15 16:06:01,818 [trainer.py] => Average Accuracy (CNN): 86.44749999999999 

2025-07-15 16:06:01,820 [trainer.py] => All params: 88688289
2025-07-15 16:06:01,821 [trainer.py] => Trainable params: 2889633
2025-07-15 16:06:01,836 [ranpac.py] => Learning on 170-180
2025-07-15 16:06:04,411 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:06:04,413 [ranpac.py] => [Dynamic-K] Computed K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-15 16:06:04,414 [ranpac.py] => [KNN] task 8, dynamic K values: [13, 7, 11, 7, 8, 7, 3, 10, 14, 5]
2025-07-15 16:06:04,415 [ranpac.py] => [KNN] task 8, weight sparsity: 0.915, distance_metric: cosine
2025-07-15 16:06:29,102 [trainer.py] => No NME accuracy.
2025-07-15 16:06:29,103 [trainer.py] => CNN: {'total': 82.29, '00-99': 86.27, '100-109': 88.18, '110-119': 73.59, '120-129': 69.31, '130-139': 81.38, '140-149': 65.11, '150-159': 80.82, '160-169': 79.53, '170-179': 79.87, 'old': 82.43, 'new': 79.87}
2025-07-15 16:06:29,103 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13]
2025-07-15 16:06:29,103 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29]
2025-07-15 16:06:29,103 [trainer.py] => Average Accuracy (CNN): 85.98555555555555 

2025-07-15 16:06:29,104 [trainer.py] => All params: 88788289
2025-07-15 16:06:29,106 [trainer.py] => Trainable params: 2989633
2025-07-15 16:06:29,126 [ranpac.py] => Learning on 180-190
2025-07-15 16:06:33,304 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:06:33,306 [ranpac.py] => [Dynamic-K] Computed K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-15 16:06:33,306 [ranpac.py] => [KNN] task 9, dynamic K values: [13, 13, 8, 10, 8, 9, 17, 12, 14, 9]
2025-07-15 16:06:33,307 [ranpac.py] => [KNN] task 9, weight sparsity: 0.887, distance_metric: cosine
2025-07-15 16:07:01,404 [trainer.py] => No NME accuracy.
2025-07-15 16:07:01,405 [trainer.py] => CNN: {'total': 81.58, '00-99': 85.93, '100-109': 87.84, '110-119': 72.89, '120-129': 68.97, '130-139': 79.66, '140-149': 65.11, '150-159': 80.48, '160-169': 78.52, '170-179': 79.19, '180-189': 77.35, 'old': 81.81, 'new': 77.35}
2025-07-15 16:07:01,405 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13, 79.518]
2025-07-15 16:07:01,405 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29, 81.58]
2025-07-15 16:07:01,405 [trainer.py] => Average Accuracy (CNN): 85.54499999999999 

2025-07-15 16:07:01,406 [trainer.py] => All params: 88888289
2025-07-15 16:07:01,407 [trainer.py] => Trainable params: 3089633
2025-07-15 16:07:01,427 [ranpac.py] => Learning on 190-200
2025-07-15 16:07:03,927 [ranpac.py] => [Dynamic-K] Computing dynamic K for 10 new classes
2025-07-15 16:07:03,928 [ranpac.py] => [Dynamic-K] Computed K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-15 16:07:03,929 [ranpac.py] => [KNN] task 10, dynamic K values: [6, 10, 10, 7, 9, 9, 8, 5, 11, 7]
2025-07-15 16:07:03,929 [ranpac.py] => [KNN] task 10, weight sparsity: 0.918, distance_metric: cosine
2025-07-15 16:07:32,083 [trainer.py] => No NME accuracy.
2025-07-15 16:07:32,083 [trainer.py] => CNN: {'total': 80.77, '00-99': 85.1, '100-109': 88.18, '110-119': 73.94, '120-129': 68.62, '130-139': 80.34, '140-149': 64.75, '150-159': 81.16, '160-169': 78.19, '170-179': 78.52, '180-189': 77.0, '190-199': 73.31, 'old': 81.17, 'new': 73.31}
2025-07-15 16:07:32,083 [trainer.py] => CNN HM: [0.0, 88.655, 80.132, 78.482, 83.659, 76.739, 82.78, 81.882, 81.13, 79.518, 77.04]
2025-07-15 16:07:32,083 [trainer.py] => CNN top1 curve: [90.43, 89.63, 88.11, 86.39, 85.74, 84.25, 83.92, 83.11, 82.29, 81.58, 80.77]
2025-07-15 16:07:32,083 [trainer.py] => Average Accuracy (CNN): 85.11090909090909 

2025-07-15 16:07:32,085 [trainer.py] => Forgetting (CNN): 2.267000000000003
2025-07-15 16:44:03,455 [trainer.py] => config: ./exps/ranpac_bayesian.json
2025-07-15 16:44:03,456 [trainer.py] => prefix: reproduce
2025-07-15 16:44:03,456 [trainer.py] => dataset: cub
2025-07-15 16:44:03,457 [trainer.py] => memory_size: 0
2025-07-15 16:44:03,457 [trainer.py] => shuffle: True
2025-07-15 16:44:03,457 [trainer.py] => init_cls: 100
2025-07-15 16:44:03,458 [trainer.py] => increment: 10
2025-07-15 16:44:03,458 [trainer.py] => model_name: ranpac
2025-07-15 16:44:03,459 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 16:44:03,459 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 16:44:03,460 [trainer.py] => seed: 1993
2025-07-15 16:44:03,460 [trainer.py] => resume: False
2025-07-15 16:44:03,460 [trainer.py] => shot: 5
2025-07-15 16:44:03,461 [trainer.py] => use_simplecil: False
2025-07-15 16:44:03,461 [trainer.py] => tuned_epoch: 1
2025-07-15 16:44:03,461 [trainer.py] => init_lr: 0.01
2025-07-15 16:44:03,462 [trainer.py] => batch_size: 48
2025-07-15 16:44:03,463 [trainer.py] => weight_decay: 0.0005
2025-07-15 16:44:03,463 [trainer.py] => min_lr: 0
2025-07-15 16:44:03,463 [trainer.py] => ffn_num: 64
2025-07-15 16:44:03,464 [trainer.py] => optimizer: sgd
2025-07-15 16:44:03,464 [trainer.py] => use_RP: True
2025-07-15 16:44:03,464 [trainer.py] => M: 10000
2025-07-15 16:44:03,465 [trainer.py] => fecam: True
2025-07-15 16:44:03,465 [trainer.py] => calibration: True
2025-07-15 16:44:03,466 [trainer.py] => use_bayesian_cov: True
2025-07-15 16:44:03,466 [trainer.py] => bayesian_prior_strength: similarity_based
2025-07-15 16:44:03,466 [trainer.py] => bayesian_min_nu: 5
2025-07-15 16:44:03,467 [trainer.py] => bayesian_max_nu: 50
2025-07-15 16:44:03,467 [trainer.py] => bayesian_adaptive_strength: True
2025-07-15 16:44:03,467 [trainer.py] => knn_k: 5
2025-07-15 16:44:03,467 [trainer.py] => knn_distance_metric: cosine
2025-07-15 16:44:03,468 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 16:44:03,468 [trainer.py] => knn_adaptive_k: True
2025-07-15 16:44:03,468 [trainer.py] => knn_temperature: 16.0
2025-07-15 16:44:03,469 [trainer.py] => k_min: 3
2025-07-15 16:44:03,469 [trainer.py] => k_max: 21
2025-07-15 16:44:03,469 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 16:44:03,469 [trainer.py] => cosine_temperature: 16.0
2025-07-15 16:44:03,582 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 16:44:23,800 [trainer.py] => All params: 86988288
2025-07-15 16:44:23,801 [trainer.py] => Trainable params: 1189632
2025-07-15 16:44:25,413 [ranpac.py] => Learning on 0-100
2025-07-15 16:45:08,520 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 16:50:17,265 [trainer.py] => config: ./exps/ranpac_bayesian.json
2025-07-15 16:50:17,266 [trainer.py] => prefix: reproduce
2025-07-15 16:50:17,266 [trainer.py] => dataset: cub
2025-07-15 16:50:17,266 [trainer.py] => memory_size: 0
2025-07-15 16:50:17,266 [trainer.py] => shuffle: True
2025-07-15 16:50:17,266 [trainer.py] => init_cls: 100
2025-07-15 16:50:17,266 [trainer.py] => increment: 10
2025-07-15 16:50:17,266 [trainer.py] => model_name: ranpac
2025-07-15 16:50:17,266 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 16:50:17,267 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 16:50:17,267 [trainer.py] => seed: 1993
2025-07-15 16:50:17,267 [trainer.py] => resume: False
2025-07-15 16:50:17,267 [trainer.py] => shot: 5
2025-07-15 16:50:17,267 [trainer.py] => use_simplecil: False
2025-07-15 16:50:17,267 [trainer.py] => tuned_epoch: 1
2025-07-15 16:50:17,267 [trainer.py] => init_lr: 0.01
2025-07-15 16:50:17,267 [trainer.py] => batch_size: 48
2025-07-15 16:50:17,267 [trainer.py] => weight_decay: 0.0005
2025-07-15 16:50:17,267 [trainer.py] => min_lr: 0
2025-07-15 16:50:17,268 [trainer.py] => ffn_num: 64
2025-07-15 16:50:17,268 [trainer.py] => optimizer: sgd
2025-07-15 16:50:17,268 [trainer.py] => use_RP: True
2025-07-15 16:50:17,268 [trainer.py] => M: 10000
2025-07-15 16:50:17,268 [trainer.py] => fecam: True
2025-07-15 16:50:17,268 [trainer.py] => calibration: True
2025-07-15 16:50:17,268 [trainer.py] => use_bayesian_cov: True
2025-07-15 16:50:17,268 [trainer.py] => bayesian_prior_strength: similarity_based
2025-07-15 16:50:17,268 [trainer.py] => bayesian_min_nu: 5
2025-07-15 16:50:17,268 [trainer.py] => bayesian_max_nu: 50
2025-07-15 16:50:17,268 [trainer.py] => bayesian_adaptive_strength: True
2025-07-15 16:50:17,268 [trainer.py] => knn_k: 5
2025-07-15 16:50:17,269 [trainer.py] => knn_distance_metric: cosine
2025-07-15 16:50:17,269 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 16:50:17,269 [trainer.py] => knn_adaptive_k: True
2025-07-15 16:50:17,269 [trainer.py] => knn_temperature: 16.0
2025-07-15 16:50:17,269 [trainer.py] => k_min: 3
2025-07-15 16:50:17,269 [trainer.py] => k_max: 21
2025-07-15 16:50:17,269 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 16:50:17,269 [trainer.py] => cosine_temperature: 16.0
2025-07-15 16:50:17,395 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 16:50:37,926 [trainer.py] => All params: 86988288
2025-07-15 16:50:37,927 [trainer.py] => Trainable params: 1189632
2025-07-15 16:50:39,520 [ranpac.py] => Learning on 0-100
2025-07-15 16:51:09,383 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 16:52:32,166 [trainer.py] => config: ./exps/ranpac_bayesian.json
2025-07-15 16:52:32,167 [trainer.py] => prefix: reproduce
2025-07-15 16:52:32,167 [trainer.py] => dataset: cub
2025-07-15 16:52:32,167 [trainer.py] => memory_size: 0
2025-07-15 16:52:32,167 [trainer.py] => shuffle: True
2025-07-15 16:52:32,167 [trainer.py] => init_cls: 100
2025-07-15 16:52:32,167 [trainer.py] => increment: 10
2025-07-15 16:52:32,167 [trainer.py] => model_name: ranpac
2025-07-15 16:52:32,167 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 16:52:32,167 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 16:52:32,167 [trainer.py] => seed: 1993
2025-07-15 16:52:32,167 [trainer.py] => resume: False
2025-07-15 16:52:32,167 [trainer.py] => shot: 5
2025-07-15 16:52:32,168 [trainer.py] => use_simplecil: False
2025-07-15 16:52:32,168 [trainer.py] => tuned_epoch: 1
2025-07-15 16:52:32,168 [trainer.py] => init_lr: 0.01
2025-07-15 16:52:32,168 [trainer.py] => batch_size: 48
2025-07-15 16:52:32,168 [trainer.py] => weight_decay: 0.0005
2025-07-15 16:52:32,168 [trainer.py] => min_lr: 0
2025-07-15 16:52:32,168 [trainer.py] => ffn_num: 64
2025-07-15 16:52:32,168 [trainer.py] => optimizer: sgd
2025-07-15 16:52:32,168 [trainer.py] => use_RP: True
2025-07-15 16:52:32,168 [trainer.py] => M: 10000
2025-07-15 16:52:32,168 [trainer.py] => fecam: True
2025-07-15 16:52:32,168 [trainer.py] => calibration: True
2025-07-15 16:52:32,168 [trainer.py] => use_bayesian_cov: True
2025-07-15 16:52:32,168 [trainer.py] => bayesian_prior_strength: adaptive
2025-07-15 16:52:32,168 [trainer.py] => bayesian_nu_prior_base: 20
2025-07-15 16:52:32,168 [trainer.py] => bayesian_min_nu: 15
2025-07-15 16:52:32,169 [trainer.py] => bayesian_max_nu: 100
2025-07-15 16:52:32,169 [trainer.py] => bayesian_adaptive_strength: True
2025-07-15 16:52:32,169 [trainer.py] => knn_k: 5
2025-07-15 16:52:32,169 [trainer.py] => knn_distance_metric: cosine
2025-07-15 16:52:32,169 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 16:52:32,169 [trainer.py] => knn_adaptive_k: True
2025-07-15 16:52:32,169 [trainer.py] => knn_temperature: 16.0
2025-07-15 16:52:32,169 [trainer.py] => k_min: 3
2025-07-15 16:52:32,169 [trainer.py] => k_max: 21
2025-07-15 16:52:32,169 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 16:52:32,169 [trainer.py] => cosine_temperature: 16.0
2025-07-15 16:52:32,251 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 16:52:40,990 [trainer.py] => All params: 86988288
2025-07-15 16:52:40,991 [trainer.py] => Trainable params: 1189632
2025-07-15 16:52:42,599 [ranpac.py] => Learning on 0-100
2025-07-15 16:53:13,444 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
2025-07-15 17:00:23,901 [trainer.py] => config: ./exps/ranpac_bayesian.json
2025-07-15 17:00:23,901 [trainer.py] => prefix: reproduce
2025-07-15 17:00:23,901 [trainer.py] => dataset: cub
2025-07-15 17:00:23,902 [trainer.py] => memory_size: 0
2025-07-15 17:00:23,902 [trainer.py] => shuffle: True
2025-07-15 17:00:23,902 [trainer.py] => init_cls: 100
2025-07-15 17:00:23,902 [trainer.py] => increment: 10
2025-07-15 17:00:23,902 [trainer.py] => model_name: ranpac
2025-07-15 17:00:23,902 [trainer.py] => backbone_type: pretrained_vit_b16_224_adapter
2025-07-15 17:00:23,902 [trainer.py] => device: [device(type='cuda', index=0)]
2025-07-15 17:00:23,902 [trainer.py] => seed: 1993
2025-07-15 17:00:23,902 [trainer.py] => resume: False
2025-07-15 17:00:23,902 [trainer.py] => shot: 5
2025-07-15 17:00:23,903 [trainer.py] => use_simplecil: False
2025-07-15 17:00:23,903 [trainer.py] => tuned_epoch: 1
2025-07-15 17:00:23,903 [trainer.py] => init_lr: 0.01
2025-07-15 17:00:23,903 [trainer.py] => batch_size: 48
2025-07-15 17:00:23,903 [trainer.py] => weight_decay: 0.0005
2025-07-15 17:00:23,903 [trainer.py] => min_lr: 0
2025-07-15 17:00:23,903 [trainer.py] => ffn_num: 64
2025-07-15 17:00:23,903 [trainer.py] => optimizer: sgd
2025-07-15 17:00:23,903 [trainer.py] => use_RP: True
2025-07-15 17:00:23,903 [trainer.py] => M: 10000
2025-07-15 17:00:23,903 [trainer.py] => fecam: True
2025-07-15 17:00:23,904 [trainer.py] => calibration: True
2025-07-15 17:00:23,904 [trainer.py] => use_bayesian_cov: True
2025-07-15 17:00:23,904 [trainer.py] => bayesian_prior_strength: adaptive
2025-07-15 17:00:23,904 [trainer.py] => bayesian_nu_prior_base: 20
2025-07-15 17:00:23,904 [trainer.py] => bayesian_min_nu: 15
2025-07-15 17:00:23,904 [trainer.py] => bayesian_max_nu: 100
2025-07-15 17:00:23,904 [trainer.py] => bayesian_adaptive_strength: True
2025-07-15 17:00:23,904 [trainer.py] => knn_k: 5
2025-07-15 17:00:23,904 [trainer.py] => knn_distance_metric: cosine
2025-07-15 17:00:23,904 [trainer.py] => knn_weight_decay: 0.1
2025-07-15 17:00:23,905 [trainer.py] => knn_adaptive_k: True
2025-07-15 17:00:23,905 [trainer.py] => knn_temperature: 16.0
2025-07-15 17:00:23,905 [trainer.py] => k_min: 3
2025-07-15 17:00:23,905 [trainer.py] => k_max: 21
2025-07-15 17:00:23,905 [trainer.py] => dynamic_k_method: cosine_similarity
2025-07-15 17:00:23,905 [trainer.py] => cosine_temperature: 16.0
2025-07-15 17:00:24,005 [data_manager.py] => [168, 136, 51, 9, 183, 101, 171, 99, 42, 159, 191, 70, 16, 188, 27, 10, 175, 26, 68, 187, 98, 6, 85, 35, 112, 43, 100, 0, 103, 181, 88, 59, 4, 2, 116, 174, 94, 80, 106, 1, 147, 17, 141, 131, 72, 23, 173, 54, 197, 118, 87, 32, 79, 104, 91, 19, 135, 107, 178, 36, 11, 199, 142, 8, 122, 3, 28, 57, 153, 172, 190, 56, 49, 44, 97, 62, 151, 169, 194, 55, 192, 12, 189, 78, 66, 180, 15, 137, 109, 134, 92, 119, 126, 52, 170, 40, 148, 65, 144, 64, 138, 45, 77, 89, 154, 90, 71, 193, 74, 30, 113, 143, 96, 84, 67, 50, 186, 156, 69, 21, 18, 111, 108, 58, 125, 157, 150, 110, 182, 129, 166, 83, 81, 60, 13, 165, 14, 176, 63, 117, 5, 22, 145, 121, 38, 41, 82, 127, 114, 20, 31, 53, 37, 163, 196, 130, 152, 162, 86, 76, 24, 34, 184, 149, 33, 128, 198, 155, 146, 167, 139, 120, 140, 102, 47, 25, 158, 123, 46, 164, 61, 7, 115, 75, 133, 160, 105, 132, 179, 124, 48, 73, 93, 39, 95, 195, 29, 177, 185, 161]
2025-07-15 17:00:36,488 [trainer.py] => All params: 86988288
2025-07-15 17:00:36,489 [trainer.py] => Trainable params: 1189632
2025-07-15 17:00:38,232 [ranpac.py] => Learning on 0-100
2025-07-15 17:01:13,810 [ranpac.py] => Task 0, Epoch 1/1 => Loss 4.586, Train_accy 4.84, Test_accy 14.84
